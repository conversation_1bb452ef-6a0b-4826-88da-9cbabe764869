"""
启动规则引擎Web UI
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """检查依赖包"""
    required_packages = ['fastapi', 'uvicorn', 'jinja2', 'python-multipart']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 启动规则引擎Web UI")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return 1
    
    # 检查数据库
    try:
        from database_utils import db_manager
        # 确保数据库表存在
        db_manager.create_tables()
        print("✅ 数据库连接正常")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return 1
    
    # 启动Web服务
    try:
        import uvicorn
        from database_utils.ui.main import app
        
        print("✅ 所有依赖检查通过")
        print("\n🌐 启动Web服务...")
        print("📍 访问地址: http://localhost:8000")
        print("📍 API文档: http://localhost:8000/docs")
        print("\n按 Ctrl+C 停止服务")
        print("=" * 50)
        
        uvicorn.run(
            app, 
            host="0.0.0.0", 
            port=8000, 
            reload=True,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
        return 0
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
