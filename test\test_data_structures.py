"""
数据结构的单元测试
"""

import unittest
from demo_input import CreativeInfo, InferInfo, ResultInfo, StatusEnum, QualityEnum


class TestCreativeInfo(unittest.TestCase):
    """CreativeInfo数据结构测试"""

    def test_creative_info_creation(self):
        """测试CreativeInfo创建"""
        creative_info = CreativeInfo(
            creative_id=1001,
            industry="电商",
            title="测试标题",
            subtitle="测试副标题",
            image_url="http://example.com/image.jpg",
            image_ocr="测试OCR内容",
            video_id=2001,
            video_ocr=["视频OCR1", "视频OCR2"],
            ldp_url="http://example.com/landing",
            device_type="mobile",
            advertiser_id=3001,
            license_info=[{"type": "营业执照", "number": "123456"}],
            request_id="req_001"
        )

        self.assertEqual(creative_info.creative_id, 1001)
        self.assertEqual(creative_info.industry, "电商")
        self.assertEqual(creative_info.title, "测试标题")
        self.assertEqual(creative_info.subtitle, "测试副标题")
        self.assertEqual(creative_info.image_url, "http://example.com/image.jpg")
        self.assertEqual(creative_info.image_ocr, "测试OCR内容")
        self.assertEqual(creative_info.video_id, 2001)
        self.assertEqual(creative_info.video_ocr, ["视频OCR1", "视频OCR2"])
        self.assertEqual(creative_info.ldp_url, "http://example.com/landing")
        self.assertEqual(creative_info.device_type, "mobile")
        self.assertEqual(creative_info.advertiser_id, 3001)
        self.assertEqual(creative_info.license_info, [{"type": "营业执照", "number": "123456"}])
        self.assertEqual(creative_info.request_id, "req_001")

    def test_creative_info_to_json(self):
        """测试CreativeInfo转换为JSON"""
        creative_info = CreativeInfo(
            creative_id=1001,
            industry="电商",
            title="测试标题",
            subtitle="测试副标题",
            image_url="http://example.com/image.jpg",
            image_ocr="测试OCR",
            video_id=2001,
            video_ocr=["OCR1"],
            ldp_url="http://example.com/landing",
            device_type="mobile",
            advertiser_id=3001,
            license_info=[],
            request_id="req_001"
        )

        json_data = creative_info.to_json()

        self.assertIsInstance(json_data, dict)
        self.assertEqual(json_data['creative_id'], 1001)
        self.assertEqual(json_data['title'], "测试标题")
        self.assertEqual(json_data['request_id'], "req_001")

    def test_creative_info_from_json(self):
        """测试从JSON创建CreativeInfo"""
        json_data = {
            "creative_id": 1001,
            "industry": "电商",
            "title": "测试标题",
            "subtitle": "测试副标题",
            "image_url": "http://example.com/image.jpg",
            "image_ocr": "测试OCR",
            "video_id": 2001,
            "video_ocr": ["视频OCR"],
            "ldp_url": "http://example.com/landing",
            "device_type": "mobile",
            "advertiser_id": 3001,
            "license_info": [],
            "request_id": "req_001"
        }

        creative_info = CreativeInfo.from_json(json_data)

        self.assertEqual(creative_info.creative_id, 1001)
        self.assertEqual(creative_info.industry, "电商")
        self.assertEqual(creative_info.title, "测试标题")
        self.assertEqual(creative_info.subtitle, "测试副标题")
        self.assertEqual(creative_info.image_ocr, "测试OCR")
        self.assertEqual(creative_info.video_ocr, ["视频OCR"])
        self.assertEqual(creative_info.request_id, "req_001")


class TestInferInfo(unittest.TestCase):
    """InferInfo数据结构测试"""

    def test_infer_info_creation(self):
        """测试InferInfo创建"""
        infer_info = InferInfo(
            creative_id="1001",
            industry="电商",
            title=["标题1", "标题2"],
            image_url=["url1", "url2"],
            frame_url=["frame1", "frame2"],
            video_text="视频文本",
            ldp_image_url="landing_image.jpg",
            license_info={"type": "营业执照"},
            request_id="req_001"
        )

        self.assertEqual(infer_info.creative_id, "1001")
        self.assertEqual(infer_info.industry, "电商")
        self.assertEqual(infer_info.title, ["标题1", "标题2"])
        self.assertEqual(infer_info.video_text, "视频文本")
        self.assertEqual(infer_info.request_id, "req_001")

    def test_infer_info_to_json(self):
        """测试InferInfo转换为JSON"""
        infer_info = InferInfo(
            creative_id="1001",
            industry="电商",
            title=["标题1"],
            image_url=["url1"],
            frame_url=["frame1"],
            video_text="视频文本",
            ldp_image_url="landing.jpg",
            license_info={},
            request_id="req_001"
        )

        json_data = infer_info.to_json()

        self.assertIsInstance(json_data, dict)
        self.assertEqual(json_data['creative_id'], "1001")
        self.assertEqual(json_data['title'], ["标题1"])

    def test_infer_info_from_json(self):
        """测试从JSON创建InferInfo"""
        json_data = {
            "creative_id": "1001",
            "industry": "电商",
            "title": ["标题1"],
            "image_url": ["url1"],
            "frame_url": ["frame1"],
            "video_text": "视频文本",
            "ldp_image_url": "landing.jpg",
            "license_info": {},
            "request_id": "req_001"
        }

        infer_info = InferInfo.from_json(json_data)

        self.assertEqual(infer_info.creative_id, "1001")
        self.assertEqual(infer_info.title, ["标题1"])
        self.assertEqual(infer_info.video_text, "视频文本")


class TestResultInfo(unittest.TestCase):
    """ResultInfo数据结构测试"""

    def test_result_info_creation(self):
        """测试ResultInfo创建"""
        result_info = ResultInfo(
            code=200,
            requestId="req_001",
            creative_id=["1001", "1002"],
            status=StatusEnum.PASSED,
            reasons=["通过审核"],
            quality=QualityEnum.A,
            qulity_reason=["质量优秀"]
        )

        self.assertEqual(result_info.code, 200)
        self.assertEqual(result_info.requestId, "req_001")
        self.assertEqual(result_info.creative_id, ["1001", "1002"])
        self.assertEqual(result_info.status, StatusEnum.PASSED)
        self.assertEqual(result_info.reasons, ["通过审核"])
        self.assertEqual(result_info.quality, QualityEnum.A)
        self.assertEqual(result_info.qulity_reason, ["质量优秀"])

    def test_result_info_to_json(self):
        """测试ResultInfo转换为JSON"""
        result_info = ResultInfo(
            code=200,
            requestId="req_001",
            creative_id=["1001"],
            status=StatusEnum.REJECTED,
            reasons=["包含违规内容"],
            quality=QualityEnum.D,
            qulity_reason=["质量较差"]
        )

        json_data = result_info.to_json()

        self.assertIsInstance(json_data, dict)
        self.assertEqual(json_data['code'], 200)
        self.assertEqual(json_data['requestId'], "req_001")
        self.assertEqual(json_data['creative_id'], ["1001"])
        self.assertEqual(json_data['status'], "REJECTED")
        self.assertEqual(json_data['quality'], "D")
        self.assertEqual(json_data['reasons'], ["包含违规内容"])

    def test_result_info_from_json_dict(self):
        """测试从JSON字典创建ResultInfo"""
        json_data = {
            "code": 200,
            "requestId": "req_001",
            "creative_id": ["1001"],
            "status": "PASSED",
            "reasons": ["通过审核"],
            "quality": "A",
            "qulity_reason": ["质量优秀"]
        }

        result_info = ResultInfo.from_json_dict(json_data)

        self.assertEqual(result_info.code, 200)
        self.assertEqual(result_info.requestId, "req_001")
        self.assertEqual(result_info.creative_id, ["1001"])
        self.assertEqual(result_info.status, StatusEnum.PASSED)
        self.assertEqual(result_info.quality, QualityEnum.A)


class TestEnums(unittest.TestCase):
    """枚举类型测试"""

    def test_status_enum(self):
        """测试状态枚举"""
        self.assertEqual(StatusEnum.PASSED.value, "PASSED")
        self.assertEqual(StatusEnum.CHECKED.value, "CHECKED")
        self.assertEqual(StatusEnum.REJECTED.value, "REJECTED")
        self.assertEqual(StatusEnum.VL_FAILD.value, "VL_FAILD")
        self.assertEqual(StatusEnum.LLM_FAILD.value, "LLM_FAILD")
        self.assertEqual(StatusEnum.PARSE_FAILD.value, "PARSE_FAILD")

    def test_quality_enum(self):
        """测试质量枚举"""
        self.assertEqual(QualityEnum.A.value, "A")
        self.assertEqual(QualityEnum.B.value, "B")
        self.assertEqual(QualityEnum.B_PLUS.value, "B+")
        self.assertEqual(QualityEnum.C.value, "C")
        self.assertEqual(QualityEnum.D.value, "D")
        self.assertEqual(QualityEnum.REJECTED.value, "REJECTED")

    def test_enum_membership(self):
        """测试枚举成员检查"""
        # 测试StatusEnum
        self.assertIn(StatusEnum.PASSED, StatusEnum)
        self.assertIn(StatusEnum.REJECTED, StatusEnum)

        # 测试QualityEnum
        self.assertIn(QualityEnum.A, QualityEnum)
        self.assertIn(QualityEnum.REJECTED, QualityEnum)


if __name__ == '__main__':
    unittest.main()
