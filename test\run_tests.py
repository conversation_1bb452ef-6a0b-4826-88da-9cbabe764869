"""
测试运行脚本
提供多种测试运行选项，类似Makefile的功能
"""

import subprocess
import sys
import os
from pathlib import Path
import argparse
from typing import List, Optional

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent


class TestCommands:
    """测试命令类"""
    
    @staticmethod
    def run_unittest(modules: Optional[List[str]] = None, verbosity: int = 2) -> int:
        """运行unittest测试"""
        cmd = [sys.executable, "test/test_runner.py", f"--verbosity={verbosity}"]
        
        if modules:
            for module in modules:
                cmd.extend(["--module", module])
        
        return subprocess.call(cmd, cwd=PROJECT_ROOT)
    
    @staticmethod
    def run_pytest(args: Optional[List[str]] = None) -> int:
        """运行pytest测试"""
        try:
            import pytest
        except ImportError:
            print("错误: pytest未安装。请运行: pip install pytest")
            return 1
        
        cmd = [sys.executable, "-m", "pytest", "test/"]
        
        if args:
            cmd.extend(args)
        else:
            # 默认参数
            cmd.extend(["-v", "--tb=short"])
        
        return subprocess.call(cmd, cwd=PROJECT_ROOT)
    
    @staticmethod
    def run_coverage() -> int:
        """运行覆盖率测试"""
        try:
            import coverage
        except ImportError:
            print("错误: coverage未安装。请运行: pip install coverage")
            return 1
        
        # 运行覆盖率测试
        cmd = [
            sys.executable, "-m", "coverage", "run",
            "--source=.", "--omit=test/*,venv/*,env/*",
            "-m", "pytest", "test/"
        ]
        
        result = subprocess.call(cmd, cwd=PROJECT_ROOT)
        
        if result == 0:
            # 生成覆盖率报告
            subprocess.call([sys.executable, "-m", "coverage", "report"], cwd=PROJECT_ROOT)
            subprocess.call([sys.executable, "-m", "coverage", "html"], cwd=PROJECT_ROOT)
            print("\n覆盖率HTML报告已生成到 htmlcov/ 目录")
        
        return result
    
    @staticmethod
    def run_lint() -> int:
        """运行代码检查"""
        try:
            import flake8
        except ImportError:
            print("警告: flake8未安装，跳过代码检查")
            return 0
        
        cmd = [sys.executable, "-m", "flake8", ".", "--exclude=venv,env,htmlcov"]
        return subprocess.call(cmd, cwd=PROJECT_ROOT)
    
    @staticmethod
    def run_type_check() -> int:
        """运行类型检查"""
        try:
            import mypy
        except ImportError:
            print("警告: mypy未安装，跳过类型检查")
            return 0
        
        cmd = [sys.executable, "-m", "mypy", ".", "--ignore-missing-imports"]
        return subprocess.call(cmd, cwd=PROJECT_ROOT)
    
    @staticmethod
    def run_all() -> int:
        """运行所有检查和测试"""
        print("🔍 运行代码检查...")
        lint_result = TestCommands.run_lint()
        
        print("\n🔍 运行类型检查...")
        type_result = TestCommands.run_type_check()
        
        print("\n🧪 运行单元测试...")
        test_result = TestCommands.run_unittest()
        
        print("\n📊 运行覆盖率测试...")
        coverage_result = TestCommands.run_coverage()
        
        # 汇总结果
        print("\n" + "="*60)
        print("测试汇总")
        print("="*60)
        print(f"代码检查: {'✅ 通过' if lint_result == 0 else '❌ 失败'}")
        print(f"类型检查: {'✅ 通过' if type_result == 0 else '❌ 失败'}")
        print(f"单元测试: {'✅ 通过' if test_result == 0 else '❌ 失败'}")
        print(f"覆盖率测试: {'✅ 通过' if coverage_result == 0 else '❌ 失败'}")
        
        return max(lint_result, type_result, test_result, coverage_result)
    
    @staticmethod
    def install_dev_deps() -> int:
        """安装开发依赖"""
        deps = [
            "pytest>=7.0.0",
            "coverage>=6.0",
            "flake8>=4.0.0",
            "mypy>=0.900",
            "pytest-cov>=3.0.0",
            "pytest-mock>=3.0.0"
        ]
        
        cmd = [sys.executable, "-m", "pip", "install"] + deps
        return subprocess.call(cmd)
    
    @staticmethod
    def clean() -> int:
        """清理测试文件"""
        import shutil
        
        patterns = [
            PROJECT_ROOT / "htmlcov",
            PROJECT_ROOT / ".coverage",
            PROJECT_ROOT / ".pytest_cache",
            PROJECT_ROOT / "__pycache__",
        ]
        
        for pattern in patterns:
            if pattern.exists():
                if pattern.is_dir():
                    shutil.rmtree(pattern)
                    print(f"删除目录: {pattern}")
                else:
                    pattern.unlink()
                    print(f"删除文件: {pattern}")
        
        # 递归删除__pycache__目录
        for pycache in PROJECT_ROOT.rglob("__pycache__"):
            shutil.rmtree(pycache)
            print(f"删除缓存: {pycache}")
        
        print("清理完成")
        return 0


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="测试运行脚本")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # unittest命令
    unittest_parser = subparsers.add_parser("unittest", help="运行unittest测试")
    unittest_parser.add_argument("--module", "-m", action="append", help="指定测试模块")
    unittest_parser.add_argument("--verbosity", "-v", type=int, default=2, help="详细程度")
    
    # pytest命令
    pytest_parser = subparsers.add_parser("pytest", help="运行pytest测试")
    pytest_parser.add_argument("args", nargs="*", help="pytest参数")
    
    # 其他命令
    subparsers.add_parser("coverage", help="运行覆盖率测试")
    subparsers.add_parser("lint", help="运行代码检查")
    subparsers.add_parser("typecheck", help="运行类型检查")
    subparsers.add_parser("all", help="运行所有检查和测试")
    subparsers.add_parser("install-dev", help="安装开发依赖")
    subparsers.add_parser("clean", help="清理测试文件")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    # 执行对应命令
    if args.command == "unittest":
        return TestCommands.run_unittest(args.module, args.verbosity)
    elif args.command == "pytest":
        return TestCommands.run_pytest(args.args)
    elif args.command == "coverage":
        return TestCommands.run_coverage()
    elif args.command == "lint":
        return TestCommands.run_lint()
    elif args.command == "typecheck":
        return TestCommands.run_type_check()
    elif args.command == "all":
        return TestCommands.run_all()
    elif args.command == "install-dev":
        return TestCommands.install_dev_deps()
    elif args.command == "clean":
        return TestCommands.clean()
    else:
        print(f"未知命令: {args.command}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
