{% extends "base.html" %}

{% block title %}概览 - 规则引擎管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt"></i>
        系统概览
    </h1>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">词表数量</div>
                        <div class="h5 mb-0 font-weight-bold">{{ stats.wordlist_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-list fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">总词汇数</div>
                        <div class="h5 mb-0 font-weight-bold">{{ stats.total_words }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-font fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">条件数量</div>
                        <div class="h5 mb-0 font-weight-bold">{{ stats.condition_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-filter fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">活跃规则</div>
                        <div class="h5 mb-0 font-weight-bold">{{ stats.active_rule_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-sitemap fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近的词表和规则 -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-list"></i>
                    最近的词表
                </h6>
            </div>
            <div class="card-body">
                {% if recent_wordlists %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>名称</th>
                                <th>词汇数</th>
                                <th>描述</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for wordlist in recent_wordlists %}
                            <tr>
                                <td><strong>{{ wordlist.name }}</strong></td>
                                <td><span class="badge bg-primary">{{ wordlist.word_count }}</span></td>
                                <td>{{ wordlist.description or '无描述' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-center">
                    <a href="/wordlists" class="btn btn-primary btn-sm">
                        查看全部词表 <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
                {% else %}
                <div class="text-center text-muted">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>暂无词表数据</p>
                    <a href="/wordlists" class="btn btn-primary">创建第一个词表</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-sitemap"></i>
                    最近的规则
                </h6>
            </div>
            <div class="card-body">
                {% if recent_rules %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>规则名称</th>
                                <th>优先级</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for rule in recent_rules %}
                            <tr>
                                <td><strong>{{ rule.rule_name }}</strong></td>
                                <td><span class="badge bg-info">{{ rule.priority }}</span></td>
                                <td>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check"></i> 启用
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-center">
                    <a href="/rules" class="btn btn-primary btn-sm">
                        查看全部规则 <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
                {% else %}
                <div class="text-center text-muted">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>暂无规则数据</p>
                    <a href="/rules" class="btn btn-primary">创建第一个规则</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-bolt"></i>
                    快速操作
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <a href="/wordlists" class="btn btn-outline-primary btn-lg w-100">
                            <i class="fas fa-plus-circle fa-2x mb-2"></i><br>
                            创建词表
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/conditions" class="btn btn-outline-info btn-lg w-100">
                            <i class="fas fa-filter fa-2x mb-2"></i><br>
                            管理条件
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/rules" class="btn btn-outline-success btn-lg w-100">
                            <i class="fas fa-sitemap fa-2x mb-2"></i><br>
                            管理规则
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/test" class="btn btn-outline-warning btn-lg w-100">
                            <i class="fas fa-vial fa-2x mb-2"></i><br>
                            测试审核
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
