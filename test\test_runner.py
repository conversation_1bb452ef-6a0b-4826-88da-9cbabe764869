"""
测试运行器
支持运行所有测试或指定的测试模块
"""

import unittest
import sys
import os
from pathlib import Path
from typing import Optional, List
import argparse

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class TestRunner:
    """测试运行器类"""
    
    def __init__(self, verbosity: int = 2):
        """
        初始化测试运行器
        
        Args:
            verbosity: 详细程度 (0=静默, 1=正常, 2=详细)
        """
        self.verbosity = verbosity
        self.test_dir = Path(__file__).parent
        
    def discover_tests(self, pattern: str = "test_*.py") -> unittest.TestSuite:
        """
        发现测试用例
        
        Args:
            pattern: 测试文件模式
            
        Returns:
            TestSuite: 测试套件
        """
        loader = unittest.TestLoader()
        suite = loader.discover(
            start_dir=str(self.test_dir),
            pattern=pattern,
            top_level_dir=str(project_root)
        )
        return suite
    
    def run_tests(self, test_suite: Optional[unittest.TestSuite] = None) -> unittest.TestResult:
        """
        运行测试
        
        Args:
            test_suite: 测试套件，如果为None则发现所有测试
            
        Returns:
            TestResult: 测试结果
        """
        if test_suite is None:
            test_suite = self.discover_tests()
        
        runner = unittest.TextTestRunner(
            verbosity=self.verbosity,
            stream=sys.stdout,
            buffer=True
        )
        
        return runner.run(test_suite)
    
    def run_specific_tests(self, test_modules: List[str]) -> unittest.TestResult:
        """
        运行指定的测试模块
        
        Args:
            test_modules: 测试模块名列表
            
        Returns:
            TestResult: 测试结果
        """
        loader = unittest.TestLoader()
        suite = unittest.TestSuite()
        
        for module_name in test_modules:
            try:
                # 导入测试模块
                module = __import__(f"test.{module_name}", fromlist=[module_name])
                # 加载测试
                module_suite = loader.loadTestsFromModule(module)
                suite.addTest(module_suite)
            except ImportError as e:
                print(f"警告: 无法导入测试模块 {module_name}: {e}")
        
        return self.run_tests(suite)
    
    def run_test_class(self, module_name: str, class_name: str) -> unittest.TestResult:
        """
        运行指定的测试类
        
        Args:
            module_name: 模块名
            class_name: 类名
            
        Returns:
            TestResult: 测试结果
        """
        loader = unittest.TestLoader()
        
        try:
            module = __import__(f"test.{module_name}", fromlist=[module_name])
            test_class = getattr(module, class_name)
            suite = loader.loadTestsFromTestCase(test_class)
            return self.run_tests(suite)
        except (ImportError, AttributeError) as e:
            print(f"错误: 无法加载测试类 {module_name}.{class_name}: {e}")
            return unittest.TestResult()
    
    def run_test_method(self, module_name: str, class_name: str, method_name: str) -> unittest.TestResult:
        """
        运行指定的测试方法
        
        Args:
            module_name: 模块名
            class_name: 类名
            method_name: 方法名
            
        Returns:
            TestResult: 测试结果
        """
        try:
            module = __import__(f"test.{module_name}", fromlist=[module_name])
            test_class = getattr(module, class_name)
            suite = unittest.TestSuite()
            suite.addTest(test_class(method_name))
            return self.run_tests(suite)
        except (ImportError, AttributeError) as e:
            print(f"错误: 无法加载测试方法 {module_name}.{class_name}.{method_name}: {e}")
            return unittest.TestResult()
    
    def print_test_summary(self, result: unittest.TestResult):
        """
        打印测试摘要
        
        Args:
            result: 测试结果
        """
        print("\n" + "="*60)
        print("测试摘要")
        print("="*60)
        print(f"运行测试数: {result.testsRun}")
        print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
        print(f"失败: {len(result.failures)}")
        print(f"错误: {len(result.errors)}")
        print(f"跳过: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
        
        if result.failures:
            print(f"\n失败的测试:")
            for test, traceback in result.failures:
                print(f"  - {test}")
        
        if result.errors:
            print(f"\n错误的测试:")
            for test, traceback in result.errors:
                print(f"  - {test}")
        
        success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100 if result.testsRun > 0 else 0
        print(f"\n成功率: {success_rate:.1f}%")
        
        if result.wasSuccessful():
            print("✅ 所有测试通过!")
        else:
            print("❌ 部分测试失败")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="规则引擎测试运行器")
    parser.add_argument(
        "--module", "-m",
        help="运行指定的测试模块 (例如: test_database)",
        action="append"
    )
    parser.add_argument(
        "--class", "-c",
        help="运行指定的测试类 (格式: module.class)",
        dest="test_class"
    )
    parser.add_argument(
        "--method", "-t",
        help="运行指定的测试方法 (格式: module.class.method)",
        dest="test_method"
    )
    parser.add_argument(
        "--verbosity", "-v",
        type=int,
        choices=[0, 1, 2],
        default=2,
        help="详细程度 (0=静默, 1=正常, 2=详细)"
    )
    parser.add_argument(
        "--pattern", "-p",
        default="test_*.py",
        help="测试文件模式 (默认: test_*.py)"
    )
    
    args = parser.parse_args()
    
    # 创建测试运行器
    runner = TestRunner(verbosity=args.verbosity)
    
    # 根据参数运行不同的测试
    if args.test_method:
        # 运行指定方法
        parts = args.test_method.split('.')
        if len(parts) != 3:
            print("错误: 方法格式应为 module.class.method")
            return 1
        result = runner.run_test_method(parts[0], parts[1], parts[2])
    elif args.test_class:
        # 运行指定类
        parts = args.test_class.split('.')
        if len(parts) != 2:
            print("错误: 类格式应为 module.class")
            return 1
        result = runner.run_test_class(parts[0], parts[1])
    elif args.module:
        # 运行指定模块
        result = runner.run_specific_tests(args.module)
    else:
        # 运行所有测试
        print("运行所有测试...")
        result = runner.run_tests(runner.discover_tests(args.pattern))
    
    # 打印摘要
    runner.print_test_summary(result)
    
    # 返回适当的退出码
    return 0 if result.wasSuccessful() else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
