<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}规则引擎管理系统{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 0.5rem;
            margin: 0.2rem 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1rem 1rem 0 0 !important;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-cogs"></i>
                            规则引擎
                        </h4>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.url.path == '/' %}active{% endif %}" href="/">
                                <i class="fas fa-tachometer-alt"></i>
                                概览
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'wordlists' in request.url.path %}active{% endif %}" href="/wordlists">
                                <i class="fas fa-list"></i>
                                词表管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'conditions' in request.url.path %}active{% endif %}" href="/conditions">
                                <i class="fas fa-filter"></i>
                                条件管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'rules' in request.url.path %}active{% endif %}" href="/rules">
                                <i class="fas fa-sitemap"></i>
                                规则管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'test' in request.url.path %}active{% endif %}" href="/test">
                                <i class="fas fa-vial"></i>
                                测试工具
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="pt-3 pb-2 mb-3">
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>

    <!-- 通用提示框 -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 11">
        <div id="globalToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i id="toastIcon" class="fas fa-info-circle me-2"></i>
                <strong id="toastTitle" class="me-auto">提示</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="toastMessage">
                消息内容
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
    // 全局提示函数
    function showToast(message, type = 'info', title = '提示') {
        const toast = document.getElementById('globalToast');
        const toastIcon = document.getElementById('toastIcon');
        const toastTitle = document.getElementById('toastTitle');
        const toastMessage = document.getElementById('toastMessage');
        const toastHeader = toast.querySelector('.toast-header');

        // 重置样式
        toastHeader.className = 'toast-header';

        // 设置图标和颜色
        switch(type) {
            case 'success':
                toastIcon.className = 'fas fa-check-circle me-2 text-success';
                toastHeader.classList.add('bg-success', 'text-white');
                title = title || '成功';
                break;
            case 'error':
                toastIcon.className = 'fas fa-exclamation-circle me-2 text-danger';
                toastHeader.classList.add('bg-danger', 'text-white');
                title = title || '错误';
                break;
            case 'warning':
                toastIcon.className = 'fas fa-exclamation-triangle me-2 text-warning';
                toastHeader.classList.add('bg-warning', 'text-dark');
                title = title || '警告';
                break;
            default:
                toastIcon.className = 'fas fa-info-circle me-2 text-info';
                toastHeader.classList.add('bg-info', 'text-white');
                title = title || '提示';
        }

        toastTitle.textContent = title;
        toastMessage.textContent = message;

        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: 3000
        });
        bsToast.show();
    }
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
