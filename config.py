"""
配置文件
管理数据库连接和其他系统设置
"""

import os
from pathlib import Path

# 项目根目录
BASE_DIR = Path(__file__).parent

# 数据库配置
DATABASE_CONFIG = {
    'url': f"sqlite:///{BASE_DIR}/database_utils/database/rule_engine.db",
    'echo': False,  # 设置为True可以看到SQL语句
}

# 支持的字段类型映射
FIELD_TYPE_MAPPING = {
    'title': '标题',
    'subtitle': '副标题',
    'ocr_content': 'OCR内容',
    'landing_url': '落地页URL',
    'advertiser_id': '广告主ID',
    'industry': '行业'
}

# 支持的操作类型映射
OPERATOR_TYPE_MAPPING = {
    'contains': '包含',
    'not_contains': '不包含',
    'equals': '等于',
    'not_equals': '不等于',
    'in_wordlist': '在词表中',
    'not_in_wordlist': '不在词表中'
}

# 词表用途示例（仅供参考，实际用途由条件表决定）
WORDLIST_USAGE_EXAMPLES = [
    '黑名单',
    '白名单',
    '敏感词',
    '行业词',
    '品牌词',
    '违禁词'
]

# 规则状态映射
RULE_STATUS_MAPPING = {
    'active': '启用',
    'inactive': '禁用'
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': f"{BASE_DIR}/logs/rule_engine.log"
}

# 确保日志目录存在
os.makedirs(f"{BASE_DIR}/logs", exist_ok=True)
