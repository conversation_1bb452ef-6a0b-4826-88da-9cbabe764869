"""
最终综合测试
验证所有修复后的匹配逻辑
"""

from demo_input import CreativeInfo
from rule_engine_main import audit_creative_info

def test_comprehensive_matching():
    """综合测试所有匹配逻辑"""
    print("🧪 最终综合测试 - 验证所有修复")
    print("=" * 60)
    
    test_cases = [
        {
            "name": "精确ID匹配 - 白名单67890",
            "creative": CreativeInfo(
                creative_id=4001,
                industry="电商",
                title="测试广告",
                subtitle="",
                image_url="",
                image_ocr="",
                video_id=0,
                video_ocr=[],
                ldp_url="https://example.com",
                device_type="mobile",
                advertiser_id=67890,  # 精确匹配白名单
                license_info=[],
                request_id="final_001"
            ),
            "expected_whitelist": True,
            "expected_decision": "approve"
        },
        {
            "name": "部分ID匹配 - 6789不在白名单",
            "creative": CreativeInfo(
                creative_id=4002,
                industry="电商",
                title="测试广告",
                subtitle="",
                image_url="",
                image_ocr="",
                video_id=0,
                video_ocr=[],
                ldp_url="https://example.com",
                device_type="mobile",
                advertiser_id=6789,  # 部分匹配，不应该命中
                license_info=[],
                request_id="final_002"
            ),
            "expected_whitelist": False,
            "expected_decision": "approve"
        },
        {
            "name": "超集ID匹配 - 6789011不在白名单",
            "creative": CreativeInfo(
                creative_id=4003,
                industry="电商",
                title="测试广告",
                subtitle="",
                image_url="",
                image_ocr="",
                video_id=0,
                video_ocr=[],
                ldp_url="https://example.com",
                device_type="mobile",
                advertiser_id=6789011,  # 包含67890但不应该命中
                license_info=[],
                request_id="final_003"
            ),
            "expected_whitelist": False,
            "expected_decision": "approve"
        },
        {
            "name": "违规词检测 - 免费",
            "creative": CreativeInfo(
                creative_id=4004,
                industry="电商",
                title="免费领取商品",  # 包含违规词
                subtitle="",
                image_url="",
                image_ocr="",
                video_id=0,
                video_ocr=[],
                ldp_url="https://example.com",
                device_type="mobile",
                advertiser_id=12345,
                license_info=[],
                request_id="final_004"
            ),
            "expected_violation": True,
            "expected_decision": "reject"
        },
        {
            "name": "金融广告有风险提示",
            "creative": CreativeInfo(
                creative_id=4005,
                industry="金融",
                title="投资理财产品",
                subtitle="投资有风险，理财需谨慎",  # 包含风险提示
                image_url="",
                image_ocr="",
                video_id=0,
                video_ocr=[],
                ldp_url="https://example.com",
                device_type="mobile",
                advertiser_id=12345,
                license_info=[],
                request_id="final_005"
            ),
            "expected_finance_approve": True,
            "expected_decision": "approve"
        },
        {
            "name": "金融广告无风险提示",
            "creative": CreativeInfo(
                creative_id=4006,
                industry="金融",
                title="投资理财产品",
                subtitle="",  # 没有风险提示
                image_url="",
                image_ocr="",
                video_id=0,
                video_ocr=[],
                ldp_url="https://example.com",
                device_type="mobile",
                advertiser_id=12345,
                license_info=[],
                request_id="final_006"
            ),
            "expected_finance_reject": True,
            "expected_decision": "reject"
        },
        {
            "name": "优先级测试 - 白名单vs违规词",
            "creative": CreativeInfo(
                creative_id=4007,
                industry="电商",
                title="免费领取商品",  # 违规词(优先级10)
                subtitle="",
                image_url="",
                image_ocr="",
                video_id=0,
                video_ocr=[],
                ldp_url="https://example.com",
                device_type="mobile",
                advertiser_id=67890,  # 白名单(优先级9)
                license_info=[],
                request_id="final_007"
            ),
            "expected_violation": True,
            "expected_whitelist": True,
            "expected_decision": "reject"  # 违规词优先级更高
        }
    ]
    
    print(f"\n开始执行 {len(test_cases)} 个测试案例...")
    
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试案例{i}: {test_case['name']}")
        
        result = audit_creative_info(test_case['creative'])
        
        # 检查结果
        success = True
        
        # 检查最终决策
        if result.final_decision != test_case['expected_decision']:
            print(f"  ❌ 最终决策错误: 期望 {test_case['expected_decision']}, 实际 {result.final_decision}")
            success = False
        else:
            print(f"  ✅ 最终决策正确: {result.final_decision}")
        
        # 检查特定规则命中情况
        hit_rule_names = [rule.rule_name for rule in result.hit_rules]
        
        if 'expected_whitelist' in test_case:
            whitelist_hit = any("白名单" in name for name in hit_rule_names)
            if whitelist_hit != test_case['expected_whitelist']:
                print(f"  ❌ 白名单规则命中错误: 期望 {test_case['expected_whitelist']}, 实际 {whitelist_hit}")
                success = False
            else:
                print(f"  ✅ 白名单规则命中正确: {whitelist_hit}")
        
        if 'expected_violation' in test_case:
            violation_hit = any("违规词" in name for name in hit_rule_names)
            if violation_hit != test_case['expected_violation']:
                print(f"  ❌ 违规词规则命中错误: 期望 {test_case['expected_violation']}, 实际 {violation_hit}")
                success = False
            else:
                print(f"  ✅ 违规词规则命中正确: {violation_hit}")
        
        if 'expected_finance_approve' in test_case:
            finance_approve_hit = any("合规金融" in name for name in hit_rule_names)
            if finance_approve_hit != test_case['expected_finance_approve']:
                print(f"  ❌ 合规金融规则命中错误: 期望 {test_case['expected_finance_approve']}, 实际 {finance_approve_hit}")
                success = False
            else:
                print(f"  ✅ 合规金融规则命中正确: {finance_approve_hit}")
        
        if 'expected_finance_reject' in test_case:
            finance_reject_hit = any("金融行业风险提示" in name for name in hit_rule_names)
            if finance_reject_hit != test_case['expected_finance_reject']:
                print(f"  ❌ 金融风险提示规则命中错误: 期望 {test_case['expected_finance_reject']}, 实际 {finance_reject_hit}")
                success = False
            else:
                print(f"  ✅ 金融风险提示规则命中正确: {finance_reject_hit}")
        
        # 显示命中的规则
        if result.hit_rules:
            print(f"  📋 命中规则 ({len(result.hit_rules)}):")
            for rule in sorted(result.hit_rules, key=lambda x: x.priority, reverse=True):
                print(f"    - {rule.rule_name} (优先级: {rule.priority}, 动作: {rule.action})")
        else:
            print("  📋 未命中任何规则")
        
        if success:
            passed += 1
            print(f"  🎉 测试案例{i} 通过")
        else:
            failed += 1
            print(f"  💥 测试案例{i} 失败")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果统计:")
    print(f"  总测试数: {len(test_cases)}")
    print(f"  通过: {passed}")
    print(f"  失败: {failed}")
    print(f"  成功率: {passed/len(test_cases)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 所有测试通过！词表匹配逻辑修复成功！")
    else:
        print(f"\n⚠️  有 {failed} 个测试失败，需要进一步检查")

if __name__ == "__main__":
    test_comprehensive_matching()
