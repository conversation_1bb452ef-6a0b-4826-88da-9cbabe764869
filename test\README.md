# 测试框架说明

本项目包含完整的单元测试框架，支持unittest和pytest两种测试运行方式。

## 📁 测试文件结构

```
test/
├── __init__.py                    # 测试包初始化
├── README.md                      # 测试说明文档
├── conftest.py                    # pytest配置和夹具
├── test_runner.py                 # unittest测试运行器
├── run_tests.py                   # 多功能测试脚本
├── test_database.py               # 数据库功能测试
├── test_rule_engine.py            # 规则引擎功能测试
├── test_data_structures.py        # 数据结构测试
└── test_integration_pytest.py     # pytest集成测试示例
```

## 🚀 快速开始

### 运行所有测试

```bash
# 使用unittest
python test/test_runner.py

# 使用pytest (需要先安装: pip install pytest)
python -m pytest test/ -v
```

### 运行指定模块的测试

```bash
# 运行数据库测试
python test/test_runner.py --module test_database

# 运行规则引擎测试
python test/test_runner.py --module test_rule_engine

# 运行数据结构测试
python test/test_runner.py --module test_data_structures
```

### 运行指定测试类

```bash
python test/test_runner.py --class test_database.TestDatabaseManager
```

### 运行指定测试方法

```bash
python test/test_runner.py --method test_database.TestDatabaseManager.test_create_wordlist
```

## 🛠️ 使用多功能测试脚本

`run_tests.py` 提供了类似Makefile的功能：

```bash
# 运行unittest测试
python test/run_tests.py unittest

# 运行pytest测试
python test/run_tests.py pytest

# 运行覆盖率测试
python test/run_tests.py coverage

# 运行代码检查
python test/run_tests.py lint

# 运行类型检查
python test/run_tests.py typecheck

# 运行所有检查和测试
python test/run_tests.py all

# 安装开发依赖
python test/run_tests.py install-dev

# 清理测试文件
python test/run_tests.py clean
```

## 📊 测试覆盖的功能

### 数据库测试 (`test_database.py`)
- ✅ 词表创建、添加、删除、查询
- ✅ 条件创建和查询
- ✅ 规则创建、状态更新、查询
- ✅ 数据库模型和枚举类型
- ✅ 错误处理和边界情况

### 规则引擎测试 (`test_rule_engine.py`)
- ✅ 条件ID提取
- ✅ 逻辑表达式评估
- ✅ 词表匹配功能
- ✅ 条件评估（包含、等于、词表匹配等）
- ✅ 完整的审核流程
- ✅ 审核结果数据结构

### 数据结构测试 (`test_data_structures.py`)
- ✅ CreativeInfo创建和序列化
- ✅ InferInfo创建和序列化
- ✅ ResultInfo创建和序列化
- ✅ 枚举类型（StatusEnum、QualityEnum）
- ✅ JSON转换功能

### 集成测试 (`test_integration_pytest.py`)
- ✅ 完整的审核工作流程
- ✅ 违规检测
- ✅ 特定广告主监控
- ✅ 参数化测试
- ✅ 性能测试

## 🔧 Python 3.10+ 特性使用

测试代码充分利用了Python 3.10+的新特性：

### 1. 结构化模式匹配 (Match-Case)
```python
# 在测试中可以使用match-case进行条件判断
match test_result.status:
    case "PASSED":
        assert test_result.is_success
    case "FAILED":
        assert not test_result.is_success
    case _:
        raise ValueError("Unknown status")
```

### 2. 类型提示增强
```python
from typing import Optional, List, Dict, Any, Union

def test_function(data: Dict[str, Any]) -> Optional[List[str]]:
    """使用更精确的类型提示"""
    pass
```

### 3. 数据类和属性
```python
from dataclasses import dataclass, field

@dataclass
class TestConfig:
    """测试配置数据类"""
    verbosity: int = 2
    modules: List[str] = field(default_factory=list)
    timeout: float = 30.0
```

### 4. 上下文管理器增强
```python
# 使用新的上下文管理器语法
with (
    patch('module.function') as mock_func,
    tempfile.NamedTemporaryFile() as temp_file
):
    # 测试代码
    pass
```

## 🎯 测试最佳实践

### 1. 测试隔离
- 每个测试使用独立的临时数据库
- 使用mock避免外部依赖
- 测试后自动清理资源

### 2. 测试数据管理
- 使用夹具(fixtures)提供测试数据
- 参数化测试减少重复代码
- 边界值和异常情况测试

### 3. 断言策略
```python
# 使用具体的断言方法
self.assertEqual(actual, expected)
self.assertIsInstance(obj, ExpectedType)
self.assertIn(item, container)

# pytest风格断言
assert actual == expected
assert isinstance(obj, ExpectedType)
assert item in container
```

### 4. 性能测试
```python
import time

def test_performance():
    start_time = time.time()
    # 执行被测试的代码
    execution_time = time.time() - start_time
    assert execution_time < 1.0, f"执行时间过长: {execution_time:.2f}秒"
```

## 📈 持续集成

测试框架支持CI/CD集成：

```yaml
# GitHub Actions示例
- name: Run Tests
  run: |
    python test/run_tests.py all
    
- name: Upload Coverage
  run: |
    python test/run_tests.py coverage
```

## 🐛 调试测试

### 1. 详细输出
```bash
python test/test_runner.py --verbosity=2
```

### 2. 单独运行失败的测试
```bash
python -m unittest test.test_module.TestClass.test_method -v
```

### 3. 使用pdb调试
```python
import pdb; pdb.set_trace()  # 在测试中添加断点
```

## 📝 编写新测试

### 1. 创建新的测试文件
```python
import unittest
from your_module import YourClass

class TestYourClass(unittest.TestCase):
    def setUp(self):
        """测试前准备"""
        self.instance = YourClass()
    
    def test_your_method(self):
        """测试你的方法"""
        result = self.instance.your_method()
        self.assertIsNotNone(result)

if __name__ == '__main__':
    unittest.main()
```

### 2. 使用pytest风格
```python
import pytest
from your_module import YourClass

class TestYourClass:
    def test_your_method(self):
        """测试你的方法"""
        instance = YourClass()
        result = instance.your_method()
        assert result is not None
```

## 🔍 测试报告

运行测试后会生成详细的测试报告，包括：
- 测试通过率
- 失败和错误的详细信息
- 执行时间统计
- 覆盖率报告（如果启用）

测试框架确保代码质量和功能正确性，为项目的稳定发展提供保障。
