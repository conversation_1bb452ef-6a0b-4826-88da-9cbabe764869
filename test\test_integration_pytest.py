"""
使用pytest的集成测试示例
展示如何使用pytest和夹具进行测试
"""

import pytest
from unittest.mock import patch

from rule_engine_main import RuleEngine, audit_creative_info


@pytest.mark.integration
class TestRuleEngineIntegration:
    """规则引擎集成测试（使用pytest）"""
    
    def test_complete_audit_workflow(self, setup_test_rules, sample_creative_data):
        """测试完整的审核工作流程"""
        test_data = setup_test_rules
        db_manager = test_data['db_manager']
        
        # 使用patch替换全局db_manager
        with patch('rule_engine_main.db_manager', db_manager):
            rule_engine = RuleEngine()
            
            # 测试正常广告
            normal_creative = sample_creative_data['normal']
            result = rule_engine.audit_creative(normal_creative)
            
            assert result.creative_id == normal_creative.creative_id
            assert result.request_id == normal_creative.request_id
            assert not result.is_hit  # 正常广告不应该命中规则
            assert len(result.hit_rules) == 0
            assert result.total_rules_checked > 0
    
    def test_violation_detection(self, setup_test_rules, sample_creative_data):
        """测试违规检测"""
        test_data = setup_test_rules
        db_manager = test_data['db_manager']
        
        with patch('rule_engine_main.db_manager', db_manager):
            rule_engine = RuleEngine()
            
            # 测试违规广告
            violation_creative = sample_creative_data['violation']
            result = rule_engine.audit_creative(violation_creative)
            
            assert result.is_hit  # 违规广告应该命中规则
            assert len(result.hit_rules) > 0
            
            # 检查命中的规则
            rule_names = {rule.rule_name for rule in result.hit_rules}
            assert "高风险广告检测" in rule_names
            assert "特定广告主监控" in rule_names
    
    def test_specific_advertiser_monitoring(self, setup_test_rules, sample_creative_data):
        """测试特定广告主监控"""
        test_data = setup_test_rules
        db_manager = test_data['db_manager']
        
        with patch('rule_engine_main.db_manager', db_manager):
            rule_engine = RuleEngine()
            
            # 测试特定广告主
            specific_creative = sample_creative_data['specific_advertiser']
            result = rule_engine.audit_creative(specific_creative)
            
            assert result.is_hit  # 特定广告主应该命中规则
            
            # 检查命中的规则
            rule_names = {rule.rule_name for rule in result.hit_rules}
            assert "特定广告主监控" in rule_names
    
    @pytest.mark.parametrize("creative_type,expected_hit", [
        ("normal", False),
        ("violation", True),
        ("specific_advertiser", True)
    ])
    def test_audit_results_parametrized(self, setup_test_rules, sample_creative_data, creative_type, expected_hit):
        """参数化测试不同类型的创意"""
        test_data = setup_test_rules
        db_manager = test_data['db_manager']
        
        with patch('rule_engine_main.db_manager', db_manager):
            rule_engine = RuleEngine()
            
            creative = sample_creative_data[creative_type]
            result = rule_engine.audit_creative(creative)
            
            assert result.is_hit == expected_hit
            if expected_hit:
                assert len(result.hit_rules) > 0
            else:
                assert len(result.hit_rules) == 0


@pytest.mark.unit
class TestRuleEngineComponents:
    """规则引擎组件单元测试（使用pytest）"""
    
    def test_extract_condition_ids_various_formats(self):
        """测试各种格式的条件ID提取"""
        rule_engine = RuleEngine()
        
        test_cases = [
            ("1", [1]),
            ("1 AND 2", [1, 2]),
            ("(1 AND 2) OR 3", [1, 2, 3]),
            ("((1 AND 2) OR (3 AND 4)) AND 5", [1, 2, 3, 4, 5]),
            ("NOT 1", [1]),
            ("1 OR NOT 2", [1, 2])
        ]
        
        for expression, expected in test_cases:
            result = rule_engine._extract_condition_ids(expression)
            assert sorted(result) == sorted(expected), f"Failed for expression: {expression}"
    
    def test_evaluate_logic_expression_complex(self):
        """测试复杂逻辑表达式评估"""
        rule_engine = RuleEngine()
        
        # 测试复杂的逻辑组合
        test_cases = [
            ("(1 AND 2) OR (3 AND 4)", {"1": True, "2": False, "3": True, "4": True}, True),
            ("(1 AND 2) OR (3 AND 4)", {"1": False, "2": False, "3": False, "4": True}, False),
            ("1 AND (2 OR 3)", {"1": True, "2": False, "3": True}, True),
            ("1 AND (2 OR 3)", {"1": False, "2": True, "3": True}, False),
        ]
        
        for expression, conditions, expected in test_cases:
            result = rule_engine._evaluate_logic_expression(expression, conditions)
            assert result == expected, f"Failed for expression: {expression} with conditions: {conditions}"
    
    def test_wordlist_matching_edge_cases(self, temp_database):
        """测试词表匹配的边界情况"""
        db_manager = temp_database
        
        # 创建测试词表
        wordlist_id = db_manager.create_wordlist("测试词表", "测试")
        db_manager.add_words_to_wordlist(wordlist_id, ["测试词", "特殊字符@#$", ""])
        
        with patch('rule_engine_main.db_manager', db_manager):
            rule_engine = RuleEngine()
            
            # 测试各种匹配情况
            test_cases = [
                ("包含测试词的文本", True, True),
                ("不包含的文本", True, False),
                ("包含特殊字符@#$的文本", True, True),
                ("空文本", True, False),
                ("", True, False),
            ]
            
            for text, should_match, expected in test_cases:
                result = rule_engine._check_wordlist_match(text, wordlist_id, should_match)
                assert result == expected, f"Failed for text: '{text}', should_match: {should_match}"


@pytest.mark.slow
class TestPerformance:
    """性能测试"""
    
    def test_large_wordlist_performance(self, temp_database):
        """测试大词表的性能"""
        db_manager = temp_database
        
        # 创建大词表
        wordlist_id = db_manager.create_wordlist("大词表", "性能测试")
        large_wordlist = [f"词汇{i}" for i in range(1000)]
        db_manager.add_words_to_wordlist(wordlist_id, large_wordlist)
        
        with patch('rule_engine_main.db_manager', db_manager):
            rule_engine = RuleEngine()
            
            # 测试匹配性能
            import time
            start_time = time.time()
            
            for i in range(100):
                text = f"这是包含词汇{i}的测试文本"
                result = rule_engine._check_wordlist_match(text, wordlist_id, True)
                assert result == True
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 性能断言（100次匹配应该在1秒内完成）
            assert execution_time < 1.0, f"性能测试失败，执行时间: {execution_time:.2f}秒"
    
    def test_complex_rule_performance(self, setup_test_rules, sample_creative_data):
        """测试复杂规则的性能"""
        test_data = setup_test_rules
        db_manager = test_data['db_manager']
        
        # 创建复杂规则
        complex_rule_id = db_manager.create_rule(
            rule_name="复杂规则",
            rule_description="包含多个条件的复杂规则",
            logic_expression=f"({test_data['condition_ids'][0]} AND {test_data['condition_ids'][1]}) OR ({test_data['condition_ids'][2]} AND {test_data['condition_ids'][0]})",
            priority=15
        )
        
        with patch('rule_engine_main.db_manager', db_manager):
            rule_engine = RuleEngine()
            
            # 测试性能
            import time
            start_time = time.time()
            
            for _ in range(50):
                for creative in sample_creative_data.values():
                    result = rule_engine.audit_creative(creative)
                    assert result is not None
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 性能断言（150次审核应该在2秒内完成）
            assert execution_time < 2.0, f"性能测试失败，执行时间: {execution_time:.2f}秒"
