"""
数据库相关功能的单元测试
"""

import unittest
import tempfile
import os
from pathlib import Path
from unittest.mock import patch

from database_utils import (
    DatabaseManager, Wordlist, WordlistItem, Condition, Rule,
    FieldType, OperatorType, RuleStatus
)


class TestDatabaseManager(unittest.TestCase):
    """数据库管理器测试类"""

    def setUp(self):
        """测试前准备"""
        # 创建临时数据库文件
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.temp_db.close()
        self.db_url = f"sqlite:///{self.temp_db.name}"

        # 创建测试用的数据库管理器
        self.db_manager = DatabaseManager(self.db_url)
        self.db_manager.create_tables()

    def tearDown(self):
        """测试后清理"""
        # 关闭数据库连接
        if hasattr(self.db_manager, 'engine'):
            self.db_manager.engine.dispose()

        # 删除临时数据库文件
        try:
            if os.path.exists(self.temp_db.name):
                os.unlink(self.temp_db.name)
        except PermissionError:
            # 在Windows上可能会有权限问题，忽略
            pass

    def test_create_wordlist(self):
        """测试创建词表"""
        wordlist_id = self.db_manager.create_wordlist("测试词表", "这是一个测试词表")
        self.assertIsInstance(wordlist_id, int)
        self.assertGreater(wordlist_id, 0)

    def test_add_words_to_wordlist(self):
        """测试向词表添加词汇"""
        # 创建词表
        wordlist_id = self.db_manager.create_wordlist("测试词表", "测试描述")

        # 添加词汇
        words = ["测试词1", "测试词2", "测试词3"]
        result = self.db_manager.add_words_to_wordlist(wordlist_id, words)
        self.assertTrue(result)

        # 验证词汇是否添加成功
        retrieved_words = self.db_manager.get_wordlist(wordlist_id)
        self.assertEqual(set(words), set(retrieved_words))

    def test_remove_words_from_wordlist(self):
        """测试从词表删除词汇"""
        # 创建词表并添加词汇
        wordlist_id = self.db_manager.create_wordlist("测试词表", "测试描述")
        words = ["词1", "词2", "词3"]
        self.db_manager.add_words_to_wordlist(wordlist_id, words)

        # 删除部分词汇
        words_to_remove = ["词1", "词3"]
        result = self.db_manager.remove_words_from_wordlist(wordlist_id, words_to_remove)
        self.assertTrue(result)

        # 验证删除结果
        remaining_words = self.db_manager.get_wordlist(wordlist_id)
        self.assertEqual(["词2"], remaining_words)

    def test_get_all_wordlists(self):
        """测试获取所有词表"""
        # 创建多个词表
        wordlist1_id = self.db_manager.create_wordlist("词表1", "描述1")
        wordlist2_id = self.db_manager.create_wordlist("词表2", "描述2")

        # 添加一些词汇
        self.db_manager.add_words_to_wordlist(wordlist1_id, ["词1", "词2"])
        self.db_manager.add_words_to_wordlist(wordlist2_id, ["词3"])

        # 获取所有词表
        wordlists = self.db_manager.get_all_wordlists()
        self.assertEqual(len(wordlists), 2)

        # 验证词表信息
        wordlist_names = {wl['name'] for wl in wordlists}
        self.assertEqual(wordlist_names, {"词表1", "词表2"})

        # 验证词汇数量
        for wordlist in wordlists:
            if wordlist['name'] == "词表1":
                self.assertEqual(wordlist['word_count'], 2)
            elif wordlist['name'] == "词表2":
                self.assertEqual(wordlist['word_count'], 1)

    def test_create_condition(self):
        """测试创建条件"""
        # 创建词表
        wordlist_id = self.db_manager.create_wordlist("测试词表", "测试")

        # 创建使用词表的条件
        condition_id = self.db_manager.create_condition(
            condition_name="测试条件",
            field_name="title",
            operator="in_wordlist",
            wordlist_id=wordlist_id
        )
        self.assertIsInstance(condition_id, int)
        self.assertGreater(condition_id, 0)

        # 创建使用值的条件
        condition_id2 = self.db_manager.create_condition(
            condition_name="测试条件2",
            field_name="subtitle",
            operator="contains",
            value="测试值"
        )
        self.assertIsInstance(condition_id2, int)
        self.assertGreater(condition_id2, 0)

    def test_get_condition(self):
        """测试获取条件"""
        # 创建条件
        condition_id = self.db_manager.create_condition(
            condition_name="测试条件",
            field_name="title",
            operator="contains",
            value="测试值"
        )

        # 获取条件
        condition = self.db_manager.get_condition(condition_id)
        self.assertIsNotNone(condition)
        self.assertEqual(condition['condition_name'], "测试条件")
        self.assertEqual(condition['field_name'], "title")
        self.assertEqual(condition['operator'], "contains")
        self.assertEqual(condition['value'], "测试值")

    def test_create_rule(self):
        """测试创建规则"""
        # 创建条件
        condition_id = self.db_manager.create_condition(
            condition_name="测试条件",
            field_name="title",
            operator="contains",
            value="测试"
        )

        # 创建规则
        rule_id = self.db_manager.create_rule(
            rule_name="测试规则",
            rule_description="这是一个测试规则",
            logic_expression=f"{condition_id}",
            priority=5
        )
        self.assertIsInstance(rule_id, int)
        self.assertGreater(rule_id, 0)

    def test_get_active_rules(self):
        """测试获取启用的规则"""
        # 创建条件
        condition_id = self.db_manager.create_condition(
            condition_name="测试条件",
            field_name="title",
            operator="contains",
            value="测试"
        )

        # 创建多个规则
        rule1_id = self.db_manager.create_rule(
            rule_name="规则1",
            logic_expression=f"{condition_id}",
            priority=10
        )
        rule2_id = self.db_manager.create_rule(
            rule_name="规则2",
            logic_expression=f"{condition_id}",
            priority=5
        )

        # 禁用一个规则
        self.db_manager.update_rule_status(rule2_id, "inactive")

        # 获取启用的规则
        active_rules = self.db_manager.get_active_rules()
        self.assertEqual(len(active_rules), 1)
        self.assertEqual(active_rules[0]['rule_name'], "规则1")
        self.assertEqual(active_rules[0]['priority'], 10)

    def test_delete_wordlist(self):
        """测试删除词表"""
        # 创建词表并添加词汇
        wordlist_id = self.db_manager.create_wordlist("测试词表", "测试")
        self.db_manager.add_words_to_wordlist(wordlist_id, ["词1", "词2"])

        # 删除词表
        result = self.db_manager.delete_wordlist(wordlist_id)
        self.assertTrue(result)

        # 验证词表已删除
        wordlists = self.db_manager.get_all_wordlists()
        self.assertEqual(len(wordlists), 0)


class TestDatabaseModels(unittest.TestCase):
    """数据库模型测试类"""

    def test_field_type_enum(self):
        """测试字段类型枚举"""
        self.assertEqual(FieldType.TITLE.value, "title")
        self.assertEqual(FieldType.SUBTITLE.value, "subtitle")
        self.assertEqual(FieldType.OCR_CONTENT.value, "ocr_content")
        self.assertEqual(FieldType.LANDING_URL.value, "landing_url")
        self.assertEqual(FieldType.ADVERTISER_ID.value, "advertiser_id")
        self.assertEqual(FieldType.INDUSTRY.value, "industry")

    def test_operator_type_enum(self):
        """测试操作类型枚举"""
        self.assertEqual(OperatorType.CONTAINS.value, "contains")
        self.assertEqual(OperatorType.NOT_CONTAINS.value, "not_contains")
        self.assertEqual(OperatorType.EQUALS.value, "equals")
        self.assertEqual(OperatorType.NOT_EQUALS.value, "not_equals")
        self.assertEqual(OperatorType.IN_WORDLIST.value, "in_wordlist")
        self.assertEqual(OperatorType.NOT_IN_WORDLIST.value, "not_in_wordlist")

    def test_rule_status_enum(self):
        """测试规则状态枚举"""
        self.assertEqual(RuleStatus.ACTIVE.value, "active")
        self.assertEqual(RuleStatus.INACTIVE.value, "inactive")


if __name__ == '__main__':
    unittest.main()
