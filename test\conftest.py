"""
pytest配置文件
提供测试夹具和配置
"""

import pytest
import tempfile
import os
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from database_utils import DatabaseManager


@pytest.fixture
def temp_database():
    """
    临时数据库夹具
    为每个测试创建一个独立的临时数据库
    """
    # 创建临时数据库文件
    temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
    temp_db.close()
    db_url = f"sqlite:///{temp_db.name}"
    
    # 创建数据库管理器并初始化表
    db_manager = DatabaseManager(db_url)
    db_manager.create_tables()
    
    yield db_manager
    
    # 清理临时文件
    if os.path.exists(temp_db.name):
        os.unlink(temp_db.name)


@pytest.fixture
def sample_wordlist_data():
    """
    示例词表数据夹具
    """
    return {
        'black_words': ["零元购", "免费领取", "投资理财", "高收益"],
        'white_words': ["官方", "正品", "品牌授权", "质量保证"],
        'sensitive_words': ["投资有风险", "收益不保证", "理财需谨慎"]
    }


@pytest.fixture
def sample_creative_data():
    """
    示例创意数据夹具
    """
    from demo_input import CreativeInfo
    
    return {
        'normal': CreativeInfo(
            creative_id=1001,
            industry="电商",
            title="品牌官方旗舰店",
            subtitle="正品保证，投资有风险",
            image_url="http://example.com/image1.jpg",
            image_ocr="品牌授权",
            video_id=0,
            video_ocr=[],
            ldp_url="http://example.com/landing1",
            device_type="mobile",
            advertiser_id=67890,
            license_info=[],
            request_id="req_001"
        ),
        'violation': CreativeInfo(
            creative_id=1002,
            industry="金融",
            title="零元购买理财产品",
            subtitle="高收益保证",
            image_url="http://example.com/image2.jpg",
            image_ocr="免费领取",
            video_id=0,
            video_ocr=[],
            ldp_url="http://example.com/landing2",
            device_type="mobile",
            advertiser_id=12345,
            license_info=[],
            request_id="req_002"
        ),
        'specific_advertiser': CreativeInfo(
            creative_id=1003,
            industry="教育",
            title="在线课程推广",
            subtitle="优质教育资源",
            image_url="http://example.com/image3.jpg",
            image_ocr="学习资料",
            video_id=0,
            video_ocr=[],
            ldp_url="http://example.com/landing3",
            device_type="mobile",
            advertiser_id=12345,
            license_info=[],
            request_id="req_003"
        )
    }


@pytest.fixture
def setup_test_rules(temp_database, sample_wordlist_data):
    """
    设置测试规则夹具
    创建完整的测试数据：词表、条件、规则
    """
    db_manager = temp_database
    
    # 创建词表
    black_wordlist_id = db_manager.create_wordlist("测试黑名单", "用于测试的黑名单词表")
    white_wordlist_id = db_manager.create_wordlist("测试白名单", "用于测试的白名单词表")
    sensitive_wordlist_id = db_manager.create_wordlist("测试敏感词", "用于测试的敏感词表")
    
    # 添加词汇
    db_manager.add_words_to_wordlist(black_wordlist_id, sample_wordlist_data['black_words'])
    db_manager.add_words_to_wordlist(white_wordlist_id, sample_wordlist_data['white_words'])
    db_manager.add_words_to_wordlist(sensitive_wordlist_id, sample_wordlist_data['sensitive_words'])
    
    # 创建条件
    condition1_id = db_manager.create_condition(
        condition_name="标题包含黑名单词汇",
        field_name="title",
        operator="in_wordlist",
        wordlist_id=black_wordlist_id
    )
    
    condition2_id = db_manager.create_condition(
        condition_name="副标题不包含风险提示",
        field_name="subtitle",
        operator="not_contains",
        value="投资有风险"
    )
    
    condition3_id = db_manager.create_condition(
        condition_name="特定广告主检查",
        field_name="advertiser_id",
        operator="equals",
        value="12345"
    )
    
    # 创建规则
    rule1_id = db_manager.create_rule(
        rule_name="高风险广告检测",
        rule_description="检测包含黑名单词汇且缺少风险提示的广告",
        logic_expression=f"({condition1_id} AND {condition2_id})",
        priority=10
    )
    
    rule2_id = db_manager.create_rule(
        rule_name="特定广告主监控",
        rule_description="监控特定广告主的广告",
        logic_expression=f"{condition3_id}",
        priority=5
    )
    
    return {
        'db_manager': db_manager,
        'wordlist_ids': {
            'black': black_wordlist_id,
            'white': white_wordlist_id,
            'sensitive': sensitive_wordlist_id
        },
        'condition_ids': [condition1_id, condition2_id, condition3_id],
        'rule_ids': [rule1_id, rule2_id]
    }


# pytest配置
def pytest_configure(config):
    """pytest配置"""
    # 添加自定义标记
    config.addinivalue_line(
        "markers", "slow: 标记测试为慢速测试"
    )
    config.addinivalue_line(
        "markers", "integration: 标记测试为集成测试"
    )
    config.addinivalue_line(
        "markers", "unit: 标记测试为单元测试"
    )


def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    # 为没有标记的测试添加unit标记
    for item in items:
        if not any(item.iter_markers()):
            item.add_marker(pytest.mark.unit)
