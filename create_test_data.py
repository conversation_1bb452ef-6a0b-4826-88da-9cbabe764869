"""
创建测试数据
包含词表、条件和规则，演示新的规则动作功能
"""

from database_utils import db_manager

def create_test_data():
    """创建测试数据"""
    print("开始创建测试数据...")

    try:
        # 1. 创建词表
        print("\n创建词表...")

        # 违规词表
        violation_wordlist_id = db_manager.create_wordlist(
            "违规词表",
            "包含违规、敏感词汇的词表"
        )
        violation_words = ["免费", "零元购", "保证收益", "无风险", "100%赚钱"]
        db_manager.add_words_to_wordlist(violation_wordlist_id, violation_words)
        print(f"  创建违规词表 (ID: {violation_wordlist_id})")

        # 风险提示词表
        risk_wordlist_id = db_manager.create_wordlist(
            "风险提示词表",
            "包含合规风险提示的词表"
        )
        risk_words = ["投资有风险", "理财需谨慎", "风险自担", "谨慎投资"]
        db_manager.add_words_to_wordlist(risk_wordlist_id, risk_words)
        print(f"  创建风险提示词表 (ID: {risk_wordlist_id})")

        # 白名单广告主词表
        whitelist_advertiser_id = db_manager.create_wordlist(
            "白名单广告主",
            "可信任的广告主ID列表"
        )
        whitelist_advertisers = ["67890", "11111", "22222"]
        db_manager.add_words_to_wordlist(whitelist_advertiser_id, whitelist_advertisers)
        print(f"  创建白名单广告主词表 (ID: {whitelist_advertiser_id})")

        # 2. 创建条件
        print("\n创建条件...")

        # 违规词检查条件
        violation_condition_id = db_manager.create_condition(
            condition_name="标题包含违规词",
            field_name="title",
            operator="contains_wordlist_word",
            wordlist_id=violation_wordlist_id
        )
        print(f"  创建违规词检查条件 (ID: {violation_condition_id})")

        # 风险提示检查条件
        risk_condition_id = db_manager.create_condition(
            condition_name="副标题包含风险提示",
            field_name="subtitle",
            operator="contains_wordlist_word",
            wordlist_id=risk_wordlist_id
        )
        print(f"  创建风险提示检查条件 (ID: {risk_condition_id})")

        # 白名单广告主条件
        whitelist_condition_id = db_manager.create_condition(
            condition_name="白名单广告主",
            field_name="advertiser_id",
            operator="in_wordlist",
            wordlist_id=whitelist_advertiser_id
        )
        print(f"  创建白名单广告主条件 (ID: {whitelist_condition_id})")

        # 金融行业条件
        finance_condition_id = db_manager.create_condition(
            condition_name="金融行业",
            field_name="industry",
            operator="equals",
            value="金融"
        )
        print(f"  创建金融行业条件 (ID: {finance_condition_id})")

        # 3. 创建规则
        print("\n创建规则...")

        # 高优先级拒绝规则：违规词直接拒绝
        violation_rule_id = db_manager.create_rule(
            rule_name="违规词拒绝规则",
            rule_description="标题包含违规词的广告直接拒绝",
            logic_expression=str(violation_condition_id),
            priority=10,
            action="reject"
        )
        print(f"  创建违规词拒绝规则 (ID: {violation_rule_id}, 优先级: 10, 动作: 拒绝)")

        # 高优先级通过规则：白名单广告主直接通过
        whitelist_rule_id = db_manager.create_rule(
            rule_name="白名单广告主通过规则",
            rule_description="白名单广告主的广告直接通过",
            logic_expression=str(whitelist_condition_id),
            priority=9,
            action="approve"
        )
        print(f"  创建白名单广告主通过规则 (ID: {whitelist_rule_id}, 优先级: 9, 动作: 通过)")

        # 中优先级拒绝规则：金融行业无风险提示
        finance_rule_id = db_manager.create_rule(
            rule_name="金融行业风险提示规则",
            rule_description="金融行业广告必须包含风险提示",
            logic_expression=f"{finance_condition_id} AND NOT {risk_condition_id}",
            priority=5,
            action="reject"
        )
        print(f"  创建金融行业风险提示规则 (ID: {finance_rule_id}, 优先级: 5, 动作: 拒绝)")

        # 低优先级通过规则：有风险提示的金融广告
        finance_approve_rule_id = db_manager.create_rule(
            rule_name="合规金融广告通过规则",
            rule_description="包含风险提示的金融广告可以通过",
            logic_expression=f"{finance_condition_id} AND {risk_condition_id}",
            priority=3,
            action="approve"
        )
        print(f"  创建合规金融广告通过规则 (ID: {finance_approve_rule_id}, 优先级: 3, 动作: 通过)")

        print("\n✅ 测试数据创建完成!")

        # 显示统计信息
        wordlists = db_manager.get_all_wordlists()
        conditions = db_manager.get_all_conditions()
        rules = db_manager.get_active_rules()

        print(f"\n📊 数据统计:")
        print(f"  词表数量: {len(wordlists)}")
        print(f"  条件数量: {len(conditions)}")
        print(f"  规则数量: {len(rules)}")

        print(f"\n🎯 规则优先级分布:")
        for rule in sorted(rules, key=lambda x: x['priority'], reverse=True):
            print(f"  {rule['rule_name']}: 优先级 {rule['priority']}, 动作 {rule['action']}")

    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")

if __name__ == "__main__":
    create_test_data()
