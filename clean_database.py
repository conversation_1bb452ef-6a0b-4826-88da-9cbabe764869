"""
清理数据库中的重复数据
"""

from database_utils import db_manager

def clean_database():
    """清理数据库中的所有数据"""
    print("开始清理数据库...")
    
    try:
        # 获取所有数据进行备份显示
        wordlists = db_manager.get_all_wordlists()
        conditions = db_manager.get_all_conditions()
        rules = db_manager.get_active_rules()
        
        print(f"清理前统计:")
        print(f"  词表数量: {len(wordlists)}")
        print(f"  条件数量: {len(conditions)}")
        print(f"  规则数量: {len(rules)}")
        
        # 清理规则
        print("\n清理规则...")
        for rule in rules:
            success = db_manager.delete_rule(rule['id'])
            if success:
                print(f"  删除规则: {rule['rule_name']} (ID: {rule['id']})")
        
        # 清理条件
        print("\n清理条件...")
        for condition in conditions:
            success = db_manager.delete_condition(condition['id'])
            if success:
                print(f"  删除条件: {condition['condition_name']} (ID: {condition['id']})")
        
        # 清理词表
        print("\n清理词表...")
        for wordlist in wordlists:
            success = db_manager.delete_wordlist(wordlist['id'])
            if success:
                print(f"  删除词表: {wordlist['name']} (ID: {wordlist['id']})")
        
        # 验证清理结果
        wordlists_after = db_manager.get_all_wordlists()
        conditions_after = db_manager.get_all_conditions()
        rules_after = db_manager.get_active_rules()
        
        print(f"\n清理后统计:")
        print(f"  词表数量: {len(wordlists_after)}")
        print(f"  条件数量: {len(conditions_after)}")
        print(f"  规则数量: {len(rules_after)}")
        
        print("\n✅ 数据库清理完成!")
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")

if __name__ == "__main__":
    clean_database()
