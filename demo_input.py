# encoding=utf-8
"""数据结构定义"""
from dataclasses import dataclass
from enum import Enum


@dataclass
class CreativeInfo:
    """原始创意信息"""

    # 创意id
    creative_id: int
    # 行业
    industry: str
    # 文本标题
    title: str
    # 文本副标题
    subtitle: str
    # 图像url
    image_url: str
    # 图像ocr
    image_ocr: str
    # 视频vid
    video_id: int
    # 视频ocr,抽多帧
    video_ocr: list[str]
    # 落地页
    ldp_url: str
    # 端类型
    device_type: str
    # 广告主id
    advertiser_id: int
    # 资质信息
    license_info: list[dict]
    # 唯一的请求ID。用于日志追踪和问题排查
    request_id: str

    def to_json(self):
        """将对象转换为字典形式以便于序列化"""
        return self.__dict__

    @staticmethod
    def from_json(json_dict: dict) -> "CreativeInfo":
        """
        从给定的字典中创建一个新的 CreativeInfo 对象
        :param json_dict: 包含创意信息的字典
        :return: 新的 CreativeInfo 对象
        """
        return CreativeInfo(
            creative_id=json_dict.get("creative_id", None),
            industry=json_dict.get("industry", None),
            title=json_dict.get("title", None),
            subtitle=json_dict.get("subtitle", None),
            image_url=json_dict.get("image_url", None),
            image_ocr=json_dict.get("image_ocr", None),
            video_id=json_dict.get("video_id", None),
            video_ocr=json_dict.get("video_ocr", []),
            ldp_url=json_dict.get("ldp_url", None),
            device_type=json_dict.get("device_type", None),
            advertiser_id=json_dict.get("advertiser_id", None),
            license_info=json_dict.get("license_info", []),
            request_id=json_dict.get("request_id", None),
        )


@dataclass
class InferInfo:
    """模型推理信息"""

    creative_id: str
    industry: str
    title: list[str]
    image_url: list[str]
    frame_url: list[str]
    video_text: str
    ldp_image_url: str
    # 资质OCR信息
    license_info: dict
    # 唯一的请求ID。用于日志追踪和问题排查
    request_id: str

    def to_json(self):
        """将对象转换为字典形式以便于序列化"""
        return self.__dict__

    @staticmethod
    def from_json(json_dict: dict) -> "InferInfo":
        """
        从给定的字典中创建一个新的 InferInfo 对象
        :param json_dict: 包含推理信息的字典
        :return: 新的 InferInfo 对象
        """
        return InferInfo(
            creative_id=json_dict.get("creative_id", None),
            industry=json_dict.get("industry", None),
            title=json_dict.get("title", None),
            image_url=json_dict.get("image_url", None),
            frame_url=json_dict.get("frame_url", None),
            video_text=json_dict.get("video_text", None),
            ldp_image_url=json_dict.get("ldp_image_url", None),
            license_info=json_dict.get("license_info", None),
            request_id=json_dict.get("request_id", None),
        )


class StatusEnum(Enum):
    # 合规状态枚举
    PASSED = "PASSED"
    CHECKED = "CHECKED"
    REJECTED = "REJECTED"
    VL_FAILD = "VL_FAILD"
    LLM_FAILD = "LLM_FAILD"
    PARSE_FAILD = "PARSE_FAILD"


class QualityEnum(Enum):
    # 质量等级枚举
    A = "A"
    B = "B"
    B_PLUS = "B+"
    C = "C"
    D = "D"
    REJECTED = "REJECTED"


@dataclass
class ResultInfo:
    """机审结果信息"""

    code: int
    requestId: str
    creative_id: list[str]
    status: StatusEnum
    reasons: list[str]
    quality: QualityEnum
    qulity_reason: list[str]

    def to_json(self):
        """将对象转换为字典形式以便于序列化"""
        return {
            "code": self.code,
            "requestId": self.requestId,
            "creative_id": self.creative_id,  # 注意这里应为 self.creative_id 而非 self.creativeId
            "status": self.status.value,
            "reasons": self.reasons,
            "quality": self.quality.value if self.quality else None,
            "qulity_reason": self.qulity_reason,
        }

    @staticmethod
    def from_json_dict(json_dict: dict) -> "ResultInfo":
        """
        从给定的字典中创建一个新的 ResultInfo 对象
        :param json_dict: 包含审核结果信息的字典
        :return: 新的 ResultInfo 对象
        """
        return ResultInfo(  # 注意这里应为 ResultInfo 而非 InferInfo
            code=json_dict.get("code", None),
            requestId=json_dict.get("requestId", None),
            creative_id=json_dict.get(
                "creative_id", None
            ),  # 注意这里应为 "creative_id"
            status=StatusEnum(json_dict.get("status", None)),  # 将字符串转换为枚举值
            reasons=json_dict.get("reasons", None),  # 将原因列表转换为枚举值
            quality=QualityEnum(json_dict.get("quality", None)),  # 将字符串转换为枚举值
            qulity_reason=json_dict.get("qulity_reason", None),
        )
