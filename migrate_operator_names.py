#!/usr/bin/env python3
"""
操作符名称迁移脚本
将数据库中的旧操作符名称更新为新的名称
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database_utils import db_manager
from database_utils.models import Condition, OperatorType
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine

def migrate_operator_names():
    """迁移操作符名称"""
    print("🔄 开始迁移操作符名称...")
    
    try:
        # 获取数据库引擎和会话
        engine = db_manager.engine
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # 查找需要更新的条件
        old_to_new_mapping = {
            'in_wordlist': 'contains_wordlist_word',
            'not_in_wordlist': 'not_contains_wordlist_word'
        }
        
        updated_count = 0
        
        for old_operator, new_operator in old_to_new_mapping.items():
            # 查找使用旧操作符的条件
            conditions = session.query(Condition).filter(
                Condition.operator == old_operator
            ).all()
            
            print(f"📋 找到 {len(conditions)} 个使用 '{old_operator}' 操作符的条件")
            
            # 更新操作符
            for condition in conditions:
                print(f"   更新条件: {condition.condition_name} (ID: {condition.id})")
                condition.operator = new_operator
                updated_count += 1
        
        # 提交更改
        session.commit()
        print(f"✅ 成功更新了 {updated_count} 个条件的操作符")
        
        # 验证更新结果
        print("\n📊 更新后的操作符统计:")
        for operator_type in OperatorType:
            count = session.query(Condition).filter(
                Condition.operator == operator_type.value
            ).count()
            if count > 0:
                print(f"   {operator_type.value}: {count} 个条件")
        
        session.close()
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        if 'session' in locals():
            session.rollback()
            session.close()
        return False
    
    return True

def verify_migration():
    """验证迁移结果"""
    print("\n🔍 验证迁移结果...")
    
    try:
        # 检查是否还有旧的操作符
        old_operators = ['in_wordlist', 'not_in_wordlist']
        
        engine = db_manager.engine
        Session = sessionmaker(bind=engine)
        session = Session()
        
        for old_op in old_operators:
            count = session.query(Condition).filter(
                Condition.operator == old_op
            ).count()
            
            if count > 0:
                print(f"⚠️  警告: 仍有 {count} 个条件使用旧操作符 '{old_op}'")
                return False
            else:
                print(f"✅ 确认: 没有条件使用旧操作符 '{old_op}'")
        
        session.close()
        print("✅ 迁移验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        if 'session' in locals():
            session.close()
        return False

def main():
    """主函数"""
    print("🚀 操作符名称迁移工具")
    print("=" * 50)
    print("将旧的操作符名称更新为新的名称:")
    print("  'in_wordlist' → 'contains_wordlist_word'")
    print("  'not_in_wordlist' → 'not_contains_wordlist_word'")
    print("=" * 50)
    
    # 确认是否继续
    response = input("是否继续执行迁移? (y/N): ").strip().lower()
    if response not in ['y', 'yes', '是']:
        print("❌ 迁移已取消")
        return 1
    
    # 执行迁移
    if migrate_operator_names():
        # 验证迁移结果
        if verify_migration():
            print("\n🎉 操作符名称迁移完成!")
            print("\n💡 提示:")
            print("   - 新的操作符名称更准确地描述了实际功能")
            print("   - 'contains_wordlist_word' 表示文本中出现词表内的词条")
            print("   - 'not_contains_wordlist_word' 表示文本中未出现词表内的词条")
            return 0
        else:
            print("\n❌ 迁移验证失败，请检查数据库状态")
            return 1
    else:
        print("\n❌ 迁移失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
