"""
规则引擎使用示例
演示如何使用规则引擎进行广告审核
"""

import json
from demo_input import CreativeInfo
from rule_engine_main import audit_creative_info

def main():
    """主函数 - 演示规则引擎的基本使用"""
    print("规则引擎使用示例")
    print("=" * 50)
    
    # 创建测试创意信息
    creative_info = CreativeInfo(
        creative_id=1001,
        industry="金融",
        title="零元购买理财产品",  # 包含黑名单词汇
        subtitle="高收益保证",     # 缺少风险提示
        image_url="http://example.com/image.jpg",
        image_ocr="投资理财广告",
        video_id=0,
        video_ocr=[],
        ldp_url="http://example.com/landing",
        device_type="mobile",
        advertiser_id=12345,      # 特定广告主ID
        license_info=[],
        request_id="req_example_001"
    )
    
    print("输入的创意信息:")
    print(f"  创意ID: {creative_info.creative_id}")
    print(f"  标题: {creative_info.title}")
    print(f"  副标题: {creative_info.subtitle}")
    print(f"  广告主ID: {creative_info.advertiser_id}")
    print(f"  请求ID: {creative_info.request_id}")
    
    # 执行审核
    print("\n开始执行规则引擎审核...")
    result = audit_creative_info(creative_info)
    
    # 输出审核结果
    print("\n=== 审核结果 ===")
    print(f"创意ID: {result.creative_id}")
    print(f"请求ID: {result.request_id}")
    print(f"是否命中规则: {result.is_hit}")
    print(f"命中规则数量: {len(result.hit_rules)}")
    print(f"检查规则总数: {result.total_rules_checked}")
    print(f"审核时间: {result.audit_time}")
    
    if result.hit_rules:
        print("\n命中的规则详情:")
        for i, hit_rule in enumerate(result.hit_rules, 1):
            print(f"\n{i}. 规则ID: {hit_rule.rule_id}")
            print(f"   规则名称: {hit_rule.rule_name}")
            print(f"   规则描述: {hit_rule.rule_description}")
            print(f"   优先级: {hit_rule.priority}")
            print(f"   命中条件数: {len(hit_rule.hit_conditions)}")
            
            for j, condition in enumerate(hit_rule.hit_conditions, 1):
                print(f"     {j}) {condition['condition_name']}")
                print(f"        字段: {condition['field_name']}")
                print(f"        操作: {condition['operator']}")
                print(f"        实际值: {condition['field_value']}")
                print(f"        检查值: {condition['check_value']}")
    else:
        print("\n✓ 未命中任何规则，广告内容合规")
    
    # 输出JSON格式结果（可用于API返回）
    print(f"\nJSON格式结果:")
    print(json.dumps(result.to_dict(), ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()
