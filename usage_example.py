"""
数据库管理器使用示例
演示如何使用DatabaseManager进行各种操作
"""

from database_utils import db_manager

def setup_database():
    """初始化数据库"""
    print("创建数据库表...")
    db_manager.create_tables()
    print("数据库表创建完成！")

def demo_wordlist_operations():
    """演示词表操作"""
    print("\n=== 词表操作演示 ===")

    # 创建词表
    print("1. 创建词表...")
    black_wordlist_id = db_manager.create_wordlist("黑名单词表", "包含违规或不当词汇的词表")
    white_wordlist_id = db_manager.create_wordlist("白名单词表", "包含允许词汇的词表")
    sensitive_wordlist_id = db_manager.create_wordlist("敏感词表", "需要特别注意的敏感词汇")
    print(f"创建黑名单词表，ID: {black_wordlist_id}")
    print(f"创建白名单词表，ID: {white_wordlist_id}")
    print(f"创建敏感词表，ID: {sensitive_wordlist_id}")

    # 添加词汇到词表
    print("2. 添加词汇到词表...")
    black_words = ["零元购", "免费领取", "限时抢购", "投资理财", "高收益"]
    db_manager.add_words_to_wordlist(black_wordlist_id, black_words)
    print(f"已添加 {len(black_words)} 个黑名单词汇")

    white_words = ["官方", "正品", "品牌授权", "质量保证"]
    db_manager.add_words_to_wordlist(white_wordlist_id, white_words)
    print(f"已添加 {len(white_words)} 个白名单词汇")

    sensitive_words = ["投资有风险", "收益不保证", "理财需谨慎"]
    db_manager.add_words_to_wordlist(sensitive_wordlist_id, sensitive_words)
    print(f"已添加 {len(sensitive_words)} 个敏感词")

    # 查看所有词表
    print("3. 查看所有词表...")
    wordlists = db_manager.get_all_wordlists()
    for wordlist in wordlists:
        print(f"  词表{wordlist['id']}: {wordlist['name']} (词汇数: {wordlist['word_count']})")
        words = db_manager.get_wordlist(wordlist['id'])
        print(f"    词汇: {words}")

    return black_wordlist_id, white_wordlist_id, sensitive_wordlist_id

def demo_condition_operations(wordlist_ids):
    """演示条件操作"""
    print("\n=== 条件操作演示 ===")

    black_wordlist_id, white_wordlist_id, sensitive_wordlist_id = wordlist_ids

    # 创建条件
    print("1. 创建条件...")

    # 条件1：标题包含黑名单词汇
    condition1_id = db_manager.create_condition(
        condition_name="标题包含黑名单词汇",
        field_name="title",
        operator="contains_wordlist_word",
        wordlist_id=black_wordlist_id
    )
    print(f"创建条件1，ID: {condition1_id}")

    # 条件2：副标题不包含风险提示
    condition2_id = db_manager.create_condition(
        condition_name="副标题缺少风险提示",
        field_name="subtitle",
        operator="not_contains",
        value="投资有风险"
    )
    print(f"创建条件2，ID: {condition2_id}")

    # 条件3：广告主ID检查
    condition3_id = db_manager.create_condition(
        condition_name="特定广告主检查",
        field_name="advertiser_id",
        operator="equals",
        value="12345"
    )
    print(f"创建条件3，ID: {condition3_id}")

    # 条件4：落地页URL检查
    condition4_id = db_manager.create_condition(
        condition_name="可疑域名检查",
        field_name="landing_url",
        operator="contains",
        value="suspicious-domain.com"
    )
    print(f"创建条件4，ID: {condition4_id}")

    # 查看所有条件
    print("2. 查看所有条件...")
    conditions = db_manager.get_all_conditions()
    for condition in conditions:
        print(f"  条件{condition['id']}: {condition['condition_name']}")
        print(f"    字段: {condition['field_name']}, 操作: {condition['operator']}")
        if condition['value']:
            print(f"    值: {condition['value']}")
        if condition['wordlist_id']:
            print(f"    词表: {condition['wordlist_name']} (ID: {condition['wordlist_id']})")

    return [condition1_id, condition2_id, condition3_id, condition4_id]

def demo_rule_operations(condition_ids):
    """演示规则操作"""
    print("\n=== 规则操作演示 ===")

    # 创建规则
    print("1. 创建规则...")

    # 规则1：高风险广告检测
    rule1_id = db_manager.create_rule(
        rule_name="高风险广告检测",
        rule_description="检测包含黑名单词汇且缺少风险提示的广告",
        logic_expression=f"({condition_ids[0]} AND {condition_ids[1]})",
        priority=10
    )
    print(f"创建规则1，ID: {rule1_id}")

    # 规则2：特定广告主监控
    rule2_id = db_manager.create_rule(
        rule_name="特定广告主监控",
        rule_description="监控特定广告主或可疑域名的广告",
        logic_expression=f"{condition_ids[2]} OR {condition_ids[3]}",
        priority=5
    )
    print(f"创建规则2，ID: {rule2_id}")

    # 规则3：综合风险评估
    rule3_id = db_manager.create_rule(
        rule_name="综合风险评估",
        rule_description="综合多个条件进行风险评估",
        logic_expression=f"({condition_ids[0]} AND {condition_ids[1]}) OR ({condition_ids[2]} AND {condition_ids[3]})",
        priority=8
    )
    print(f"创建规则3，ID: {rule3_id}")

    # 查看启用的规则
    print("2. 查看启用的规则...")
    active_rules = db_manager.get_active_rules()
    for rule in active_rules:
        print(f"  规则{rule['id']}: {rule['rule_name']} (优先级: {rule['priority']})")
        print(f"    描述: {rule['rule_description']}")
        print(f"    逻辑表达式: {rule['logic_expression']}")

    return [rule1_id, rule2_id, rule3_id]

def demo_update_operations(rule_ids):
    """演示更新操作"""
    print("\n=== 更新操作演示 ===")

    # 禁用一个规则
    print("1. 禁用规则...")
    rule_id = rule_ids[1]  # 禁用第二个规则
    success = db_manager.update_rule_status(rule_id, "inactive")
    if success:
        print(f"规则 {rule_id} 已禁用")

    # 查看更新后的规则状态
    print("2. 查看更新后的启用规则...")
    active_rules = db_manager.get_active_rules()
    print(f"当前启用的规则数量: {len(active_rules)}")
    for rule in active_rules:
        print(f"  规则{rule['id']}: {rule['rule_name']}")

def demo_query_operations():
    """演示查询操作"""
    print("\n=== 查询操作演示 ===")

    # 查询特定条件
    print("1. 查询条件ID为1的详细信息...")
    condition = db_manager.get_condition(1)
    if condition:
        print(f"  条件名称: {condition['condition_name']}")
        print(f"  字段: {condition['field_name']}")
        print(f"  操作: {condition['operator']}")

    # 查询特定规则
    print("2. 查询规则ID为1的详细信息...")
    rule = db_manager.get_rule(1)
    if rule:
        print(f"  规则名称: {rule['rule_name']}")
        print(f"  描述: {rule['rule_description']}")
        print(f"  逻辑表达式: {rule['logic_expression']}")
        print(f"  状态: {rule['status']}")
        print(f"  优先级: {rule['priority']}")

def main():
    """主函数"""
    print("机审规则引擎数据库操作演示")
    print("=" * 50)

    # 初始化数据库
    setup_database()

    # 演示词表操作
    wordlist_ids = demo_wordlist_operations()

    # 演示条件操作
    condition_ids = demo_condition_operations(wordlist_ids)

    # 演示规则操作
    rule_ids = demo_rule_operations(condition_ids)

    # 演示更新操作
    demo_update_operations(rule_ids)

    # 演示查询操作
    demo_query_operations()

    print("\n演示完成！")

if __name__ == "__main__":
    main()
