"""
规则引擎功能的单元测试
"""

import unittest
import tempfile
import os
from unittest.mock import patch, MagicMock
from datetime import datetime

from demo_input import CreativeInfo
from rule_engine_main import RuleEngine, AuditResult, RuleHitInfo, audit_creative_info
from database_utils import DatabaseManager, FieldType, OperatorType


class TestRuleEngine(unittest.TestCase):
    """规则引擎测试类"""

    def setUp(self):
        """测试前准备"""
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.temp_db.close()
        self.db_url = f"sqlite:///{self.temp_db.name}"

        # 创建测试用的数据库管理器和规则引擎
        self.db_manager = DatabaseManager(self.db_url)
        self.db_manager.create_tables()

        # 使用patch替换全局db_manager
        self.patcher = patch('rule_engine_main.db_manager', self.db_manager)
        self.patcher.start()

        self.rule_engine = RuleEngine()

        # 创建测试数据
        self._setup_test_data()

    def tearDown(self):
        """测试后清理"""
        self.patcher.stop()

        # 关闭数据库连接
        if hasattr(self.db_manager, 'engine'):
            self.db_manager.engine.dispose()

        # 删除临时数据库文件
        try:
            if os.path.exists(self.temp_db.name):
                os.unlink(self.temp_db.name)
        except PermissionError:
            # 在Windows上可能会有权限问题，忽略
            pass

    def _setup_test_data(self):
        """设置测试数据"""
        # 创建测试词表
        self.black_wordlist_id = self.db_manager.create_wordlist("黑名单", "测试黑名单")
        self.white_wordlist_id = self.db_manager.create_wordlist("白名单", "测试白名单")

        # 添加测试词汇
        self.db_manager.add_words_to_wordlist(
            self.black_wordlist_id,
            ["零元购", "免费领取", "投资理财"]
        )
        self.db_manager.add_words_to_wordlist(
            self.white_wordlist_id,
            ["官方", "正品"]
        )

        # 创建测试条件
        self.condition1_id = self.db_manager.create_condition(
            condition_name="标题包含黑名单",
            field_name="title",
            operator="in_wordlist",
            wordlist_id=self.black_wordlist_id
        )

        self.condition2_id = self.db_manager.create_condition(
            condition_name="副标题不包含风险提示",
            field_name="subtitle",
            operator="not_contains",
            value="投资有风险"
        )

        self.condition3_id = self.db_manager.create_condition(
            condition_name="广告主ID检查",
            field_name="advertiser_id",
            operator="equals",
            value="12345"
        )

        # 创建测试规则
        self.rule1_id = self.db_manager.create_rule(
            rule_name="高风险广告",
            rule_description="检测高风险广告",
            logic_expression=f"({self.condition1_id} AND {self.condition2_id})",
            priority=10
        )

        self.rule2_id = self.db_manager.create_rule(
            rule_name="特定广告主",
            rule_description="监控特定广告主",
            logic_expression=f"{self.condition3_id}",
            priority=5
        )

    def test_extract_condition_ids(self):
        """测试提取条件ID"""
        # 测试简单表达式
        ids = self.rule_engine._extract_condition_ids("1 AND 2")
        self.assertEqual(ids, [1, 2])

        # 测试复杂表达式
        ids = self.rule_engine._extract_condition_ids("(1 AND 2) OR (3 AND 4)")
        self.assertEqual(sorted(ids), [1, 2, 3, 4])

        # 测试单个条件
        ids = self.rule_engine._extract_condition_ids("5")
        self.assertEqual(ids, [5])

    def test_evaluate_logic_expression(self):
        """测试逻辑表达式评估"""
        # 测试AND操作
        result = self.rule_engine._evaluate_logic_expression(
            "1 AND 2",
            {"1": True, "2": True}
        )
        self.assertTrue(result)

        result = self.rule_engine._evaluate_logic_expression(
            "1 AND 2",
            {"1": True, "2": False}
        )
        self.assertFalse(result)

        # 测试OR操作
        result = self.rule_engine._evaluate_logic_expression(
            "1 OR 2",
            {"1": False, "2": True}
        )
        self.assertTrue(result)

        # 测试复杂表达式
        result = self.rule_engine._evaluate_logic_expression(
            "(1 AND 2) OR (3 AND 4)",
            {"1": False, "2": True, "3": True, "4": True}
        )
        self.assertTrue(result)

    def test_check_wordlist_match(self):
        """测试词表匹配"""
        # 测试匹配情况
        result = self.rule_engine._check_wordlist_match(
            "这是零元购活动",
            self.black_wordlist_id,
            True
        )
        self.assertTrue(result)

        # 测试不匹配情况
        result = self.rule_engine._check_wordlist_match(
            "这是正常广告",
            self.black_wordlist_id,
            True
        )
        self.assertFalse(result)

        # 测试NOT_IN_WORDLIST
        result = self.rule_engine._check_wordlist_match(
            "这是正常广告",
            self.black_wordlist_id,
            False
        )
        self.assertTrue(result)

    def test_evaluate_condition_contains(self):
        """测试包含条件评估"""
        creative_info = CreativeInfo(
            creative_id=1,
            industry="测试",
            title="测试标题包含关键词",
            subtitle="测试副标题",
            image_url="",
            image_ocr="",
            video_id=0,
            video_ocr=[],
            ldp_url="",
            device_type="",
            advertiser_id=0,
            license_info=[],
            request_id="test"
        )

        condition = {
            'id': 1,
            'condition_name': '标题包含测试',
            'field_name': 'title',
            'operator': 'contains',
            'value': '关键词',
            'wordlist_id': None
        }

        is_hit, detail = self.rule_engine._evaluate_condition(creative_info, condition)
        self.assertTrue(is_hit)
        self.assertEqual(detail['condition_name'], '标题包含测试')
        self.assertEqual(detail['field_value'], '测试标题包含关键词')

    def test_evaluate_condition_wordlist(self):
        """测试词表条件评估"""
        creative_info = CreativeInfo(
            creative_id=1,
            industry="测试",
            title="零元购买商品",
            subtitle="",
            image_url="",
            image_ocr="",
            video_id=0,
            video_ocr=[],
            ldp_url="",
            device_type="",
            advertiser_id=0,
            license_info=[],
            request_id="test"
        )

        condition = {
            'id': 1,
            'condition_name': '标题包含黑名单',
            'field_name': 'title',
            'operator': 'in_wordlist',
            'value': None,
            'wordlist_id': self.black_wordlist_id,
            'wordlist_name': '黑名单'
        }

        is_hit, detail = self.rule_engine._evaluate_condition(creative_info, condition)
        self.assertTrue(is_hit)
        self.assertEqual(detail['field_value'], '零元购买商品')

    def test_audit_creative_no_hit(self):
        """测试审核创意 - 无命中"""
        creative_info = CreativeInfo(
            creative_id=1001,
            industry="电商",
            title="正品商品",
            subtitle="投资有风险",
            image_url="",
            image_ocr="",
            video_id=0,
            video_ocr=[],
            ldp_url="",
            device_type="",
            advertiser_id=67890,
            license_info=[],
            request_id="test_001"
        )

        result = self.rule_engine.audit_creative(creative_info)

        self.assertIsInstance(result, AuditResult)
        self.assertEqual(result.creative_id, 1001)
        self.assertEqual(result.request_id, "test_001")
        self.assertFalse(result.is_hit)
        self.assertEqual(len(result.hit_rules), 0)
        self.assertGreater(result.total_rules_checked, 0)

    def test_audit_creative_with_hit(self):
        """测试审核创意 - 有命中"""
        creative_info = CreativeInfo(
            creative_id=1002,
            industry="金融",
            title="零元购理财产品",
            subtitle="高收益保证",
            image_url="",
            image_ocr="",
            video_id=0,
            video_ocr=[],
            ldp_url="",
            device_type="",
            advertiser_id=12345,
            license_info=[],
            request_id="test_002"
        )

        result = self.rule_engine.audit_creative(creative_info)

        self.assertIsInstance(result, AuditResult)
        self.assertEqual(result.creative_id, 1002)
        self.assertTrue(result.is_hit)
        self.assertGreater(len(result.hit_rules), 0)

        # 检查命中的规则
        rule_names = {rule.rule_name for rule in result.hit_rules}
        self.assertIn("高风险广告", rule_names)
        self.assertIn("特定广告主", rule_names)

    def test_audit_result_to_dict(self):
        """测试审核结果转换为字典"""
        hit_rule = RuleHitInfo(
            rule_id=1,
            rule_name="测试规则",
            rule_description="测试描述",
            priority=5,
            hit_conditions=[{"condition_name": "测试条件"}]
        )

        result = AuditResult(
            creative_id=1001,
            request_id="test",
            is_hit=True,
            hit_rules=[hit_rule],
            audit_time=datetime(2023, 1, 1, 12, 0, 0),
            total_rules_checked=5
        )

        result_dict = result.to_dict()

        self.assertEqual(result_dict['creative_id'], 1001)
        self.assertEqual(result_dict['request_id'], "test")
        self.assertTrue(result_dict['is_hit'])
        self.assertEqual(len(result_dict['hit_rules']), 1)
        self.assertEqual(result_dict['hit_rules'][0]['rule_name'], "测试规则")
        self.assertEqual(result_dict['total_rules_checked'], 5)


class TestRuleEngineIntegration(unittest.TestCase):
    """规则引擎集成测试"""

    def test_audit_creative_info_function(self):
        """测试audit_creative_info便捷函数"""
        creative_info = CreativeInfo(
            creative_id=1001,
            industry="测试",
            title="测试标题",
            subtitle="测试副标题",
            image_url="",
            image_ocr="",
            video_id=0,
            video_ocr=[],
            ldp_url="",
            device_type="",
            advertiser_id=0,
            license_info=[],
            request_id="test"
        )

        # 这个测试使用真实的数据库，所以结果可能因数据库状态而异
        result = audit_creative_info(creative_info)
        self.assertIsInstance(result, AuditResult)
        self.assertEqual(result.creative_id, 1001)
        self.assertEqual(result.request_id, "test")


if __name__ == '__main__':
    unittest.main()
