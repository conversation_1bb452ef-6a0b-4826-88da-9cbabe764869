"""
数据库迁移脚本
添加规则动作字段和名称唯一性约束
"""

import sqlite3
from pathlib import Path
from database_utils import db_manager

def migrate_database():
    """迁移数据库结构"""
    print("开始数据库迁移...")
    
    # 获取数据库文件路径
    db_path = Path("database_utils/database/rule_engine.db")
    
    if not db_path.exists():
        print("数据库文件不存在，将创建新的数据库")
        db_manager.create_tables()
        print("✅ 新数据库创建完成")
        return
    
    try:
        # 连接数据库
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 检查是否已经有action字段
        cursor.execute("PRAGMA table_info(rule)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'action' not in columns:
            print("添加规则动作字段...")
            # 添加action字段，默认值为'reject'
            cursor.execute("""
                ALTER TABLE rule 
                ADD COLUMN action VARCHAR(10) DEFAULT 'reject'
            """)
            print("✅ 规则动作字段添加完成")
        else:
            print("✅ 规则动作字段已存在")
        
        # 检查并添加唯一性约束
        print("检查名称唯一性约束...")
        
        # 检查词表名称唯一性
        cursor.execute("""
            SELECT name, COUNT(*) as count 
            FROM wordlist 
            GROUP BY name 
            HAVING count > 1
        """)
        duplicate_wordlists = cursor.fetchall()
        
        if duplicate_wordlists:
            print(f"发现重复的词表名称: {duplicate_wordlists}")
            print("请先清理重复数据")
        else:
            print("✅ 词表名称无重复")
        
        # 检查条件名称唯一性
        cursor.execute("""
            SELECT condition_name, COUNT(*) as count 
            FROM condition 
            GROUP BY condition_name 
            HAVING count > 1
        """)
        duplicate_conditions = cursor.fetchall()
        
        if duplicate_conditions:
            print(f"发现重复的条件名称: {duplicate_conditions}")
            print("请先清理重复数据")
        else:
            print("✅ 条件名称无重复")
        
        # 检查规则名称唯一性
        cursor.execute("""
            SELECT rule_name, COUNT(*) as count 
            FROM rule 
            GROUP BY rule_name 
            HAVING count > 1
        """)
        duplicate_rules = cursor.fetchall()
        
        if duplicate_rules:
            print(f"发现重复的规则名称: {duplicate_rules}")
            print("请先清理重复数据")
        else:
            print("✅ 规则名称无重复")
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print("✅ 数据库迁移完成")
        
        # 重新创建表结构以确保所有约束生效
        print("重新初始化数据库表结构...")
        db_manager.create_tables()
        print("✅ 表结构更新完成")
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    migrate_database()
