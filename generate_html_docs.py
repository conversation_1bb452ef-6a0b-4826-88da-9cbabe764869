#!/usr/bin/env python3
"""
生成HTML格式的API文档
将Markdown格式的API文档转换为HTML格式，方便在浏览器中查看
"""

import markdown
from pathlib import Path

def generate_html_docs():
    """生成HTML格式的API文档"""
    
    # 源Markdown文档路径
    source_md = Path("database_utils/ui/API_DOCUMENTATION.md")
    
    if not source_md.exists():
        print(f"❌ 源文档不存在: {source_md}")
        return False
    
    # 读取Markdown内容
    with open(source_md, 'r', encoding='utf-8') as f:
        md_content = f.read()
    
    # 转换为HTML
    md = markdown.Markdown(extensions=['tables', 'fenced_code', 'toc'])
    html_content = md.convert(md_content)
    
    # HTML模板
    html_template = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则引擎管理系统 API 文档</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }}
        .container {{
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1, h2, h3 {{
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }}
        h1 {{
            font-size: 2.5em;
            text-align: center;
            margin-bottom: 30px;
        }}
        h2 {{
            font-size: 1.8em;
            margin-top: 40px;
        }}
        h3 {{
            font-size: 1.4em;
            margin-top: 30px;
            border-bottom: 1px solid #bdc3c7;
        }}
        code {{
            background-color: #f1f2f6;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }}
        pre {{
            background-color: #2f3640;
            color: #f5f6fa;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
        }}
        pre code {{
            background: none;
            padding: 0;
            color: inherit;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        th {{
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }}
        tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}
        .method {{
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.9em;
        }}
        .method.get {{ background-color: #27ae60; color: white; }}
        .method.post {{ background-color: #3498db; color: white; }}
        .method.put {{ background-color: #f39c12; color: white; }}
        .method.delete {{ background-color: #e74c3c; color: white; }}
        blockquote {{
            border-left: 4px solid #3498db;
            margin: 20px 0;
            padding: 10px 20px;
            background-color: #ecf0f1;
            border-radius: 0 4px 4px 0;
        }}
        .toc {{
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }}
        .toc ul {{
            list-style-type: none;
            padding-left: 20px;
        }}
        .toc a {{
            color: #2c3e50;
            text-decoration: none;
        }}
        .toc a:hover {{
            color: #3498db;
            text-decoration: underline;
        }}
    </style>
</head>
<body>
    <div class="container">
        {html_content}
        <hr style="margin: 40px 0; border: none; border-top: 2px solid #bdc3c7;">
        <p style="text-align: center; color: #7f8c8d; font-size: 0.9em;">
            本文档由系统自动生成 | 生成时间: <span id="generate-time"></span>
        </p>
    </div>
    
    <script>
        // 显示生成时间
        document.getElementById('generate-time').textContent = new Date().toLocaleString('zh-CN');
        
        // 为HTTP方法添加样式
        document.querySelectorAll('strong').forEach(function(el) {{
            const text = el.textContent.toLowerCase();
            if (['get', 'post', 'put', 'delete'].includes(text)) {{
                el.className = 'method ' + text;
            }}
        }});
    </script>
</body>
</html>"""
    
    # 输出HTML文件
    output_file = Path("api_docs.html")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_template)
    
    print(f"✅ HTML文档已生成: {output_file.absolute()}")
    print(f"   可以在浏览器中打开查看: file://{output_file.absolute()}")
    
    return True

def main():
    """主函数"""
    print("🌐 生成HTML格式API文档")
    print("=" * 40)
    
    try:
        # 检查是否安装了markdown库
        import markdown
    except ImportError:
        print("❌ 缺少markdown库，请先安装:")
        print("   pip install markdown")
        return
    
    if generate_html_docs():
        print("\n🎉 HTML文档生成完成！")
        print("\n💡 使用建议:")
        print("   1. 可以直接在浏览器中打开查看")
        print("   2. 可以部署到Web服务器供团队访问")
        print("   3. 可以转换为PDF格式进行分享")
    else:
        print("\n❌ 生成失败！")

if __name__ == "__main__":
    main()
