#!/usr/bin/env python3
"""
API文档导出工具
将API文档复制到指定目录，方便分享给其他人
"""

import os
import shutil
from pathlib import Path

def export_api_docs(target_dir=None):
    """
    导出API文档到指定目录
    
    Args:
        target_dir (str): 目标目录路径，如果为None则使用当前目录下的api_docs文件夹
    """
    # 源文档路径
    source_doc = Path("database_utils/ui/API_DOCUMENTATION.md")
    
    if not source_doc.exists():
        print(f"❌ 源文档不存在: {source_doc}")
        return False
    
    # 目标目录
    if target_dir is None:
        target_dir = Path("api_docs")
    else:
        target_dir = Path(target_dir)
    
    # 创建目标目录
    target_dir.mkdir(exist_ok=True)
    
    # 复制API文档
    target_file = target_dir / "API_DOCUMENTATION.md"
    shutil.copy2(source_doc, target_file)
    
    # 创建README文件
    readme_content = """# 规则引擎管理系统 API 文档

这是规则引擎管理系统的API接口文档。

## 文档说明

- `API_DOCUMENTATION.md`: 完整的API接口文档，包含所有接口的详细说明
- 文档格式: Markdown
- API版本: v1.0.0

## 快速开始

1. 启动规则引擎Web服务
2. 访问 http://localhost:8000/docs 查看在线API文档
3. 或者查看本目录下的 `API_DOCUMENTATION.md` 文件

## 主要功能

- 词表管理API
- 条件管理API  
- 规则管理API
- 测试API
- 统计API

## 联系方式

如有问题，请联系开发团队。
"""
    
    readme_file = target_dir / "README.md"
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ API文档已导出到: {target_dir.absolute()}")
    print(f"   - API文档: {target_file}")
    print(f"   - 说明文件: {readme_file}")
    
    return True

def main():
    """主函数"""
    print("📚 API文档导出工具")
    print("=" * 40)
    
    # 获取用户输入的目标目录
    target = input("请输入目标目录路径 (直接回车使用默认路径 './api_docs'): ").strip()
    
    if not target:
        target = None
    
    # 导出文档
    if export_api_docs(target):
        print("\n🎉 导出完成！")
        print("\n💡 使用建议:")
        print("   1. 可以将导出的文档上传到文档平台 (如GitBook、语雀等)")
        print("   2. 可以放在项目wiki或内部文档系统中")
        print("   3. 可以通过邮件或其他方式分享给团队成员")
    else:
        print("\n❌ 导出失败！")

if __name__ == "__main__":
    main()
