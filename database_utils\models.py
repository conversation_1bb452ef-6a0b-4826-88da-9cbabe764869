"""
机审规则引擎数据库模型
使用SQLAlchemy定义数据库表结构
"""

from sqlalchemy import create_engine, Column, Integer, String, Text, Boolean, ForeignKey, DateTime, Enum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from datetime import datetime
import enum

Base = declarative_base()

# 枚举定义
class FieldType(enum.Enum):
    """广告信息字段类型"""
    TITLE = "title"                    # 标题
    SUBTITLE = "subtitle"              # 副标题
    OCR_CONTENT = "ocr_content"        # OCR内容
    LANDING_URL = "landing_url"        # 落地页URL
    ADVERTISER_ID = "advertiser_id"    # 广告主ID
    INDUSTRY = "industry"              # 行业

class OperatorType(enum.Enum):
    """条件运算类型"""
    CONTAINS = "contains"              # 包含
    NOT_CONTAINS = "not_contains"      # 不包含
    EQUALS = "equals"                  # 等于
    NOT_EQUALS = "not_equals"          # 不等于
    IN_WORDLIST = "in_wordlist"        # 在词表中
    NOT_IN_WORDLIST = "not_in_wordlist"  # 不在词表中

class RuleStatus(enum.Enum):
    """规则状态"""
    ACTIVE = "active"                  # 启用
    INACTIVE = "inactive"              # 禁用

class RuleAction(enum.Enum):
    """规则动作"""
    REJECT = "reject"                  # 拒绝
    APPROVE = "approve"                # 通过

# 词表
class Wordlist(Base):
    """通用词表"""
    __tablename__ = 'wordlist'

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False, comment="词表名称")
    description = Column(Text, nullable=True, comment="词表描述")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

class WordlistItem(Base):
    """词表项"""
    __tablename__ = 'wordlist_items'

    id = Column(Integer, primary_key=True, autoincrement=True)
    wordlist_id = Column(Integer, ForeignKey('wordlist.id'), nullable=False, comment="词表ID")
    word = Column(String(255), nullable=False, comment="词条")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")

    # 关系
    wordlist = relationship("Wordlist", backref="items")

# 条件表
class Condition(Base):
    """条件表"""
    __tablename__ = 'conditions'

    id = Column(Integer, primary_key=True, autoincrement=True)
    condition_name = Column(String(100), nullable=False, comment="条件名称")
    field_name = Column(Enum(FieldType), nullable=False, comment="广告信息字段")
    operator = Column(Enum(OperatorType), nullable=False, comment="条件运算")
    value = Column(Text, nullable=True, comment="限制值（当使用词表时为空）")
    wordlist_id = Column(Integer, ForeignKey('wordlist.id'), nullable=True, comment="词表ID（当operator为词表相关操作时使用）")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    # 关系
    wordlist = relationship("Wordlist", backref="conditions")

    def __repr__(self):
        return f"<Condition(id={self.id}, name='{self.condition_name}', field='{self.field_name.value}', operator='{self.operator.value}')>"

# 规则表
class Rule(Base):
    """规则表"""
    __tablename__ = 'rules'

    id = Column(Integer, primary_key=True, autoincrement=True)
    rule_name = Column(String(100), nullable=False, comment="规则名称")
    rule_description = Column(Text, nullable=True, comment="规则详细描述")
    logic_expression = Column(Text, nullable=False, comment="逻辑表达式，如：(1 AND 2) OR (3 AND 4)")
    status = Column(Enum(RuleStatus), default=RuleStatus.ACTIVE, comment="规则状态")
    priority = Column(Integer, default=0, comment="规则优先级，数字越大优先级越高")
    action = Column(Enum(RuleAction), default=RuleAction.REJECT, comment="规则动作：拒绝或通过")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<Rule(id={self.id}, name='{self.rule_name}', status='{self.status.value}', priority={self.priority})>"

# 数据库初始化
def create_database(db_url="sqlite:///database_utils/database/rule_engine.db"):
    """创建数据库和表"""
    # 确保数据库目录存在
    import os
    os.makedirs(os.path.dirname(db_url.replace('sqlite:///', '')), exist_ok=True)

    engine = create_engine(db_url, echo=True)
    Base.metadata.create_all(engine)
    return engine

def get_session(engine):
    """获取数据库会话"""
    Session = sessionmaker(bind=engine)
    return Session()

if __name__ == "__main__":
    # 创建数据库
    engine = create_database()
    print("数据库表创建完成！")
