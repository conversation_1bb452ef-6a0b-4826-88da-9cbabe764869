{% extends "base.html" %}

{% block title %}测试工具 - 规则引擎管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-vial"></i>
        测试工具
    </h1>
</div>

<div class="row">
    <!-- 测试表单 -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-edit"></i>
                    创意信息输入
                </h6>
            </div>
            <div class="card-body">
                <form id="testForm">
                    <div class="mb-3">
                        <label for="creativeId" class="form-label">创意ID</label>
                        <input type="number" class="form-control" id="creativeId" name="creative_id" value="1001" required>
                    </div>

                    <div class="mb-3">
                        <label for="industry" class="form-label">行业</label>
                        <select class="form-select" id="industry" name="industry" required>
                            <option value="">请选择行业</option>
                            <option value="电商">电商</option>
                            <option value="金融">金融</option>
                            <option value="教育">教育</option>
                            <option value="游戏">游戏</option>
                            <option value="医疗">医疗</option>
                            <option value="房产">房产</option>
                            <option value="汽车">汽车</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="title" class="form-label">标题</label>
                        <input type="text" class="form-control" id="title" name="title"
                               placeholder="请输入广告标题" required>
                    </div>

                    <div class="mb-3">
                        <label for="subtitle" class="form-label">副标题</label>
                        <input type="text" class="form-control" id="subtitle" name="subtitle"
                               placeholder="请输入广告副标题">
                    </div>

                    <div class="mb-3">
                        <label for="imageOcr" class="form-label">图片OCR内容</label>
                        <textarea class="form-control" id="imageOcr" name="image_ocr" rows="3"
                                  placeholder="请输入图片中的文字内容"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="ldpUrl" class="form-label">落地页URL</label>
                        <input type="url" class="form-control" id="ldpUrl" name="ldp_url"
                               placeholder="https://example.com">
                    </div>

                    <div class="mb-3">
                        <label for="advertiserId" class="form-label">广告主ID</label>
                        <input type="number" class="form-control" id="advertiserId" name="advertiser_id"
                               value="0">
                    </div>

                    <div class="mb-3">
                        <label for="requestId" class="form-label">请求ID</label>
                        <input type="text" class="form-control" id="requestId" name="request_id"
                               value="web_test_001">
                    </div>

                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-play"></i>
                        开始测试
                    </button>
                </form>
            </div>
        </div>

        <!-- 快速测试案例 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-bolt"></i>
                    快速测试案例
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <button class="btn btn-outline-success btn-sm w-100" onclick="loadTestCase('normal')">
                            正常广告
                        </button>
                    </div>
                    <div class="col-md-6 mb-2">
                        <button class="btn btn-outline-danger btn-sm w-100" onclick="loadTestCase('violation')">
                            违规广告
                        </button>
                    </div>
                    <div class="col-md-6 mb-2">
                        <button class="btn btn-outline-warning btn-sm w-100" onclick="loadTestCase('financial')">
                            金融广告
                        </button>
                    </div>
                    <div class="col-md-6 mb-2">
                        <button class="btn btn-outline-info btn-sm w-100" onclick="loadTestCase('advertiser')">
                            特定广告主
                        </button>
                    </div>
                    <div class="col-md-12 mb-2">
                        <button class="btn btn-outline-primary btn-sm w-100" onclick="loadTestCase('id_exact_match')">
                            <i class="fas fa-bullseye"></i> ID精确匹配测试
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 测试结果 -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-chart-line"></i>
                    测试结果
                </h6>
            </div>
            <div class="card-body">
                <div id="testResult" class="text-center text-muted">
                    <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                    <p>请填写左侧表单并点击"开始测试"</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.result-success {
    border-left: 4px solid #28a745;
    background-color: #d4edda;
}

.result-danger {
    border-left: 4px solid #dc3545;
    background-color: #f8d7da;
}

.hit-rule {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 0.25rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
}

.condition-item {
    background-color: #e9ecef;
    border-radius: 0.25rem;
    padding: 0.5rem;
    margin: 0.25rem 0;
    font-size: 0.875rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// 测试案例数据
const testCases = {
    normal: {
        creative_id: 1001,
        industry: '电商',
        title: '品牌官方旗舰店',
        subtitle: '正品保证，投资有风险',
        image_ocr: '品牌授权',
        ldp_url: 'https://example.com/store',
        advertiser_id: 67890,
        request_id: 'test_normal'
    },
    violation: {
        creative_id: 1002,
        industry: '金融',
        title: '零元购买理财产品',
        subtitle: '高收益保证',
        image_ocr: '免费领取',
        ldp_url: 'https://example.com/finance',
        advertiser_id: 12345,
        request_id: 'test_violation'
    },
    financial: {
        creative_id: 1003,
        industry: '金融',
        title: '投资理财新机会',
        subtitle: '投资有风险，理财需谨慎',
        image_ocr: '专业理财顾问',
        ldp_url: 'https://example.com/invest',
        advertiser_id: 54321,
        request_id: 'test_financial'
    },
    advertiser: {
        creative_id: 1004,
        industry: '教育',
        title: '在线课程推广',
        subtitle: '优质教育资源',
        image_ocr: '学习资料',
        ldp_url: 'https://example.com/course',
        advertiser_id: 12345,
        request_id: 'test_advertiser'
    },
    id_exact_match: {
        creative_id: 1005,
        industry: '电商',
        title: '测试广告主ID精确匹配',
        subtitle: '白名单ID: 67890 应该命中，6789 和 6789011 不应该命中',
        image_ocr: '',
        ldp_url: 'https://example.com/test',
        advertiser_id: 67890,
        request_id: 'test_id_match'
    }
};

// 加载测试案例
function loadTestCase(caseType) {
    const testCase = testCases[caseType];
    if (!testCase) return;

    Object.keys(testCase).forEach(key => {
        const element = document.querySelector(`[name="${key}"]`);
        if (element) {
            element.value = testCase[key];
        }
    });
}

// 提交测试表单
document.getElementById('testForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // 显示加载状态
    document.getElementById('testResult').innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">测试中...</span>
            </div>
            <p class="mt-2">正在执行规则引擎测试...</p>
        </div>
    `;

    // 提交表单数据
    const formData = new FormData(this);

    fetch('/api/test-creative', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        displayTestResult(data);
    })
    .catch(error => {
        document.getElementById('testResult').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                测试失败: ${error}
            </div>
        `;
    });
});

// 显示测试结果
function displayTestResult(data) {
    const resultContainer = document.getElementById('testResult');

    if (!data.success) {
        resultContainer.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                测试失败: ${data.error}
            </div>
        `;
        return;
    }

    const result = data.result;
    const isHit = result.is_hit;
    const finalDecision = result.final_decision;
    const isApproved = finalDecision === 'approve';

    let resultHtml = `
        <div class="alert ${isApproved ? 'alert-success result-success' : 'alert-danger result-danger'}">
            <h5>
                <i class="fas fa-${isApproved ? 'check-circle' : 'exclamation-triangle'}"></i>
                最终决策：${isApproved ? '审核通过' : '审核拒绝'}
            </h5>
            <p class="mb-0">
                创意ID: ${result.creative_id} |
                请求ID: ${result.request_id} |
                检查规则数: ${result.total_rules_checked}
            </p>
        </div>
    `;

    if (isHit && result.hit_rules.length > 0) {
        resultHtml += '<h6 class="mt-3 mb-2">命中的规则:</h6>';

        result.hit_rules.forEach(rule => {
            resultHtml += `
                <div class="hit-rule">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <strong>${rule.rule_name}</strong>
                        <div>
                            <span class="badge bg-${rule.action === 'approve' ? 'success' : 'danger'} me-1">
                                ${rule.action === 'approve' ? '通过' : '拒绝'}
                            </span>
                            <span class="badge bg-secondary">优先级 ${rule.priority}</span>
                        </div>
                    </div>
                    <p class="text-muted mb-2">${rule.rule_description || '无描述'}</p>

                    <h6 class="small">命中的条件:</h6>
                    ${rule.hit_conditions.map(condition => `
                        <div class="condition-item">
                            <strong>${condition.condition_name}</strong><br>
                            <small>
                                字段: ${condition.field_name} |
                                操作: ${condition.operator} |
                                检查值: ${condition.check_value}<br>
                                实际值: <code>${condition.field_value}</code>
                            </small>
                        </div>
                    `).join('')}
                </div>
            `;
        });
    }

    // 添加详细信息
    resultHtml += `
        <div class="mt-3">
            <h6>测试详情:</h6>
            <div class="row">
                <div class="col-md-6">
                    <small class="text-muted">
                        <strong>审核时间:</strong> ${new Date(result.audit_time).toLocaleString()}<br>
                        <strong>命中规则数:</strong> ${result.hit_rules.length}<br>
                        <strong>总规则数:</strong> ${result.total_rules_checked}
                    </small>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-sm btn-outline-secondary" onclick="showJsonResult()">
                        <i class="fas fa-code"></i> 查看JSON
                    </button>
                </div>
            </div>
        </div>
    `;

    resultContainer.innerHTML = resultHtml;

    // 保存结果用于JSON查看
    window.lastTestResult = data.result;
}

// 显示JSON结果
function showJsonResult() {
    if (window.lastTestResult) {
        const jsonWindow = window.open('', '_blank');
        jsonWindow.document.write(`
            <html>
                <head><title>测试结果 JSON</title></head>
                <body>
                    <h3>测试结果 JSON</h3>
                    <pre style="background: #f8f9fa; padding: 1rem; border-radius: 0.25rem; overflow: auto;">
${JSON.stringify(window.lastTestResult, null, 2)}
                    </pre>
                </body>
            </html>
        `);
    }
}
</script>
{% endblock %}
